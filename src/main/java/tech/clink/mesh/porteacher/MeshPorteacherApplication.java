package tech.clink.mesh.porteacher;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableKafka
@SpringBootApplication
@EnableTransactionManagement
public class MeshPorteacherApplication {
    public static void main(String[] args) {
        SpringApplication.run(MeshPorteacherApplication.class, args);
    }
}
