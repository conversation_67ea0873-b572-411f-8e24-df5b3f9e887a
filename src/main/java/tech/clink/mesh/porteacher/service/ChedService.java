package tech.clink.mesh.porteacher.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import tech.clink.mesh.porteacher.rest.dto.ChedCreateDTO;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.RestUtils;

import java.util.Map;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.removeEnd;
import static org.springframework.http.HttpMethod.GET;
import static tech.clink.mesh.porteacher.util.RestUtils.createAllCEDSHeaderEntity;

@Service
@RequiredArgsConstructor
public class ChedService {
    private final Map<String, String> chedProperty;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public String create(ChedCreateDTO cedsCreateDTO) {
        String url = chedProperty.get("url");
        String password = chedProperty.get("password");
        String systemCode = chedProperty.get("systemCode");

        JsonNode body = objectMapper.valueToTree(cedsCreateDTO);
        ResponseEntity<String> httpEntity = restTemplate.exchange(url.concat("create"),
                HttpMethod.POST, RestUtils.createJSONCEDSHeaderEntity(body, systemCode, password), String.class);

        Errors.E705.thr(httpEntity.getStatusCode().is2xxSuccessful(), this.getClass().getSimpleName());
//        Preconditions.checkState(httpEntity.getStatusCode().is2xxSuccessful());

        return Objects.requireNonNull(httpEntity.getBody());
    }


    /**
     * https://wiki.edu.mos.ru/pages/viewpage.action?pageId=92027081
     * 230 М_ЦХ_01 Сохранение документа в ЦХЭД
     */
    public String create(ChedCreateDTO.Properties properties, String base64) {
        ChedCreateDTO dto = new ChedCreateDTO();
        dto.setProperties(properties);
        dto.setDocument(base64);
        dto.setDocumentClass(chedProperty.get("clazz"));
        dto.setFromSystemCode(chedProperty.get("systemCode"));
        dto.setStore(chedProperty.get("repository"));

        return create(dto);
    }

    public String getDirectLink(String uuid) {
        String link = chedProperty.get("link");
        String repository = chedProperty.get("repository");

        return link + "?os=" + repository + "&id=" + uuid;
    }

    public byte[] download(String link) {
        String url = chedProperty.get("url");
        String password = chedProperty.get("password");
        String systemCode = chedProperty.get("systemCode");

        ResponseEntity<byte[]> entity = restTemplate.exchange(url.concat(link).concat("/data"), GET,
                createAllCEDSHeaderEntity(null, systemCode, password), byte[].class);

//        Errors.E719.thr(entity.getStatusCode().is2xxSuccessful());

        return Objects.requireNonNull(entity.getBody());
    }

    public byte[] getSignature(String id, String sign) {
        String url = removeEnd(chedProperty.get("url"), "document/");
        String systemCode = chedProperty.get("systemCode");
        String password = chedProperty.get("password");

        String path = url.concat("signature/").concat(id)
                .concat("/").concat(sign).concat("/data");
        ResponseEntity<byte[]> entity = restTemplate.exchange(path, GET,
                createAllCEDSHeaderEntity(null, systemCode, password), byte[].class);

//        Errors.E719.thr(entity.getStatusCode().is2xxSuccessful());

        return Objects.requireNonNull(entity.getBody());
    }
}

