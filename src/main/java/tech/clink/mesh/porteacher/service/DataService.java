package tech.clink.mesh.porteacher.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.DeleteObjectsRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.net.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.web.multipart.MultipartFile;
import tech.clink.mesh.porteacher.model.DocumentStorage;
import tech.clink.mesh.porteacher.model.dto.aupd.AccessTokenPayloadDto;
import tech.clink.mesh.porteacher.rest.dto.ChedCreateDTO;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.Utils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.substringAfterLast;
import static org.apache.commons.lang3.math.NumberUtils.LONG_ZERO;
import static tech.clink.mesh.porteacher.util.Constants.DOT;
import static tech.clink.mesh.porteacher.util.Constants.NULL;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings({"UnstableApiUsage", "DuplicatedCode"})
public class DataService {
    private final ChedService chedService;
    private final CrudService crudService;
    private final Map<String, String> chedProperty;
    private final Map<String, String> s3Property;
    private final AmazonS3 amazonS3;

    @Value("${auds.teacher}")
    private String[] audTeacher;

    @Value("${scheduler.partitionDeleteMaterials}")
    private Integer partitionDeleteMaterials;
    @Value("${scheduler.deleteMaterialsByOne}")
    private boolean deleteByOne;

    @Value("${s3.bucket.name}")
    private String bucketName;
    @Value("${s3.proxy}")
    private String s3Proxy;

    private static final Long MAX_FILE_SIZE_IN_BYTES = 20971520L;
    private static final Set<MediaType> EXTENSIONS = ImmutableSet.of(
            MediaType.PDF, MediaType.BMP, MediaType.JPEG, MediaType.PNG,
            MediaType.parse("image/jpg")
    );

    public DocumentStorage createNewDocument(String document, String mimeType, String basic) {
        String password = chedProperty.get("password");
        String systemCode = chedProperty.get("systemCode");

        String auth = "Basic " + Base64Utils.encodeToString(
                systemCode.concat(":").concat(password).getBytes());
        Errors.E702.thr(auth.equals(basic));

        byte[] bytes = Base64Utils.decode(document.getBytes());
        String ext = StringUtils.substringAfter(mimeType, "/");

        return createNewDocument(bytes, bytes.length, mimeType,
                UUID.randomUUID().toString() + "." + ext, null);
    }

    @SneakyThrows
    public DocumentStorage createNewDocument(MultipartFile document) {
        AccessTokenPayloadDto tokenPayload = crudService.getTokenPayloadBySystem();
        return createNewDocument(document.getBytes(), document.getSize(),
                Objects.requireNonNull(document.getContentType()),
                document.getOriginalFilename(), tokenPayload);
    }

    /**
     * М_ЦХ_01 Сохранение документа в ЦХЭД
     */
    public DocumentStorage createNewDocument(byte[] file, long filesSize, String mimeType,
                                             String documentTitle, AccessTokenPayloadDto tokenPayload) {

        // 2
        Errors.E709.thr(filesSize <= MAX_FILE_SIZE_IN_BYTES);

        // 4
        Errors.E710.thr(EXTENSIONS.contains(MediaType.parse(mimeType)));

        // 5
        String documentBase64 = Base64Utils.encodeToString(file);

        // 6
        ChedCreateDTO.Properties properties = ChedCreateDTO.Properties.builder()
                .documentTitle(documentTitle)
                .mimeType(mimeType)
                .build();
        if (Objects.nonNull(tokenPayload)) {
            properties.setSsoid(tokenPayload.getSso());
        }
        String documentId = chedService.create(properties, documentBase64);
//        Errors.E705.thr(Objects.nonNull(documentId), chedService.getClass().getSimpleName());

        // 7
        String documentLink = chedService.getDirectLink(documentId);

        // 8
        DocumentStorage documentStorage = new DocumentStorage();
        documentStorage.setLink(documentLink);
        documentStorage.setSize(filesSize);
        documentStorage.setUuid(documentId);
        documentStorage.setName(FilenameUtils.getBaseName(documentTitle));
        documentStorage.setFormat(FilenameUtils.getExtension(documentTitle));

        if (Objects.nonNull(tokenPayload) && StringUtils.equalsAny(tokenPayload.getAud(), audTeacher)) {
            documentStorage.setVerification(Boolean.FALSE);
            documentStorage.setCreatedBy(tokenPayload.getStf());
        } else {
            documentStorage.setVerification(Boolean.TRUE);
            documentStorage.setCreatedBy(LONG_ZERO);
        }

        return crudService.merge(documentStorage);
    }

    public byte[] downloadFromS3(String link) throws IOException {
        return IOUtils.toByteArray(Errors.E705.thr(() ->
                amazonS3.getObject(bucketName, link).getObjectContent(), "s3"));
    }

    // todo
    public DocumentStorage createNewS3Document(String document, String name, String mimeType, String basic) {
        String password = s3Property.get("secret");
        String systemCode = s3Property.get("id");

        String auth = "Basic " + Base64Utils.encodeToString(
                systemCode.concat(":").concat(password).getBytes());
        Errors.E702.thr(auth.equals(basic));

        byte[] bytes = Base64Utils.decode(document.getBytes());
        String ext = StringUtils.substringAfter(mimeType, "/");

        return uploadToS3(bytes.length, new ByteArrayInputStream(bytes), mimeType,
                StringUtils.isBlank(name) ? null : name + DOT + ext, null);
    }

    @SneakyThrows
    public DocumentStorage createNewS3Document(MultipartFile document) {
        AccessTokenPayloadDto tokenPayload = crudService.getTokenPayloadBySystem();
        return uploadToS3(document.getSize(), document.getInputStream(),
                Objects.requireNonNull(document.getContentType()),
                document.getOriginalFilename(), tokenPayload);
    }

    /**
     * М_S3_01 Сохранение документа в s3
     */
    @SneakyThrows
    public DocumentStorage uploadToS3(long size, InputStream content, String contentType, String title, AccessTokenPayloadDto token) {

        // 2
        Errors.E709.thr(size <= MAX_FILE_SIZE_IN_BYTES);

        // 3
        MediaType mediaType = MediaType.parse(contentType);
        Errors.E710.thr(EXTENSIONS.contains(mediaType));

        // 4
        String uuid = UUID.randomUUID().toString();
        String fileName = uuid + DOT + mediaType.subtype();
        title = StringUtils.defaultIfBlank(title, fileName);
        ObjectMetadata meta = new ObjectMetadata();
        meta.setContentType(contentType);
        meta.setContentLength(size);
        // 5
        Errors.E705.thr(() -> amazonS3.putObject(bucketName, fileName, content, meta), "S3");
        // 6
        String link = s3Proxy + "/" + bucketName + "/" + fileName;

        // 7
        DocumentStorage documentStorage = new DocumentStorage();
        documentStorage.setLink(link);
        documentStorage.setSize(size);
        documentStorage.setUuid(uuid);
        documentStorage.setName(FilenameUtils.getBaseName(title));
        documentStorage.setFormat(FilenameUtils.getExtension(title));

        if (Objects.nonNull(token) && StringUtils.equalsAny(token.getAud(), audTeacher)) {
            documentStorage.setVerification(Boolean.FALSE);
            documentStorage.setCreatedBy(token.getStf());
        } else {
            documentStorage.setVerification(Boolean.TRUE);
            documentStorage.setCreatedBy(LONG_ZERO);
        }

        return crudService.merge(documentStorage);
    }

    @SneakyThrows
    public DocumentStorage download(Long docId) {
        DocumentStorage documentStorage = crudService.find(DocumentStorage.class, docId);

        String link = documentStorage.getLink();
        String url = substringAfterLast(link, "/");
        byte[] bytes;

        if (StringUtils.contains(link, "getcontent")) {
            url = substringAfterLast(link, "id=");
            bytes = chedService.download(url);
        } else {
            bytes = downloadFromS3(url);
        }
        documentStorage.setBody(bytes);
        return documentStorage;
    }

    public DocumentStorage download(String documentId) {
        DocumentStorage documentStorage = Objects.requireNonNull(crudService.find(
                DocumentStorage.class, String.class, DocumentStorage.Fields.uuid, documentId));
        documentStorage.setBody(chedService.download(documentId));
        return documentStorage;
    }

    public void delete(Set<Long> documentId) {
        delete(crudService.find(DocumentStorage.class, documentId));
    }

    public void delete(List<DocumentStorage> materials) {
        if (deleteByOne) {
            for (DocumentStorage material : materials) {
                Utils.safetyGet(() -> {
                    amazonS3.deleteObject(bucketName, substringAfterLast(material.getLink(), "/"));
                    crudService.remove(material);
                    return true;
                });
            }
        } else {
            DeleteObjectsRequest multiObjectDeleteRequest = new DeleteObjectsRequest(bucketName)
                    .withKeys(Utils.transform(materials, m -> new DeleteObjectsRequest.KeyVersion(
                            substringAfterLast(m.getLink(), "/")
                    )))
                    .withQuiet(false);
            Errors.E703.thr(() -> amazonS3.deleteObjects(multiObjectDeleteRequest));
            crudService.remove(materials);
        }
    }

    @Scheduled(cron = "${scheduler.periodDeleteMaterials}")
    private void delete() {
        delete(PageRequest.ofSize(partitionDeleteMaterials)
                .withSort(Sort.by("id")));
    }

    public void delete(Pageable pageRequest) {
        Page<DocumentStorage> page;
        do {
            page = crudService.find(DocumentStorage.class, ImmutableMap.of(
                    DocumentStorage.Fields.entityCode, NULL,
                    DocumentStorage.Fields.entityId, NULL
            ), pageRequest);
            log.info("Total count for delete {}", page.getTotalElements());
            try {
                delete(page.getContent());
            } catch (Exception e) {
                log.error("Smth went wrong", e);
            }
            pageRequest = pageRequest.next();
        } while (page.hasNext());

        log.info("Total count deleted");
    }
}
