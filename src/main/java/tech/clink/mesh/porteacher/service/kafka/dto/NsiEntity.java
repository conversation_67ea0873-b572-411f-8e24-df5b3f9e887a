package tech.clink.mesh.porteacher.service.kafka.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import tech.clink.mesh.porteacher.model.Teacher;
import tech.clink.mesh.porteacher.model.common.ReachableDTO;

public interface NsiEntity extends ReachableDTO {
    String FIELD = "nsiId";

    Long getNsiId();

    void setNsiId(Long id);

    void setTeacher(Teacher teacher);

    @JsonIgnore
    default void setDeleted(ReachableDTO teacher) {
        if (teacher.getClass().equals(Teacher.class)) {
            setTeacher((Teacher) teacher);
        }
    }

    void setIsDeleted(Boolean deleted);

    void setVerification(Boolean verification);

    void setCreatedBy(Long createdBy);
}
