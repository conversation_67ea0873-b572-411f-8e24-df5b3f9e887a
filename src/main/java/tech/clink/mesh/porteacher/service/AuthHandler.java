package tech.clink.mesh.porteacher.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jwt.SignedJWT;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.util.CastUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.WebUtils;
import tech.clink.mesh.porteacher.model.AdministratorSettings;
import tech.clink.mesh.porteacher.model.GratitudeStudent;
import tech.clink.mesh.porteacher.model.ShareLink;
import tech.clink.mesh.porteacher.model.Teacher;
import tech.clink.mesh.porteacher.model.common.TeacherEntity;
import tech.clink.mesh.porteacher.model.dto.ShareLinkDTO;
import tech.clink.mesh.porteacher.model.dto.aupd.AccessTokenPayloadDto;
import tech.clink.mesh.porteacher.model.ref.SectionRef;
import tech.clink.mesh.porteacher.rest.CheckableController;
import tech.clink.mesh.porteacher.rest.SettingsController;
import tech.clink.mesh.porteacher.util.Constants;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.Utils;
import tech.clink.mesh.porteacher.util.validation.Secured;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.google.common.collect.Iterables.getFirst;
import static com.google.common.net.HttpHeaders.AUTHORIZATION;
import static org.apache.commons.lang3.math.NumberUtils.createLong;
import static org.springframework.web.context.request.RequestContextHolder.currentRequestAttributes;
import static tech.clink.mesh.porteacher.model.ShareLink.Fields.isActive;
import static tech.clink.mesh.porteacher.model.ShareLink.Fields.url;
import static tech.clink.mesh.porteacher.model.common.IdentityEntity.Fields.id;
import static tech.clink.mesh.porteacher.util.Constants.AUPD_CURRENT_ROLE;
import static tech.clink.mesh.porteacher.util.Constants.ENTITY_CODE;
import static tech.clink.mesh.porteacher.util.Constants.IS_VISIBLE;
import static tech.clink.mesh.porteacher.util.Constants.SECTIONS;
import static tech.clink.mesh.porteacher.util.Constants.SHARE_TOKEN;

@Slf4j
@Aspect
@Order(1)
@Component
@RequiredArgsConstructor
@ConditionalOnProperty("auth.enable")
public class AuthHandler {
    private final ObjectMapper objectMapper;
    private final CrudService crudService;
    private final ConfigurableApplicationContext ctx;
    private final Set<String> teacherKey = ImmutableSet.of(
            Constants.TEACHER_ID, Constants.TEACHER__ID);
    private final Set<String> skips = ImmutableSet.of(
            "handleException", "currentVersion", "refresh", "getClazz", "getByUrl", "saveLogs", "getYamlContract");
    private static final Duration FIVE = Duration.ofMinutes(5);

    @Value("${roles.teacherId}")
    private String roleTeacher;

    @Value("${roles.adminId}")
    private String roleAdmin;

    @Value("${roles.readAdminId}")
    private String roleReadAdmin;

    @Value("${auds.teacher}")
    private String[] audTeacher;

    @Value("${auds.admin}")
    private String[] audAdmin;

    @Value("${aupd.core.keyName}")
    private String keyName;

    @Pointcut("execution(public * tech.clink.mesh.porteacher.rest..*(..))")
    public void publicRest() {

    }

    @Around("publicRest()")
    public Object userAuth(ProceedingJoinPoint pjp) throws Throwable {
        log.info("User Authorization");
        String name = pjp.getSignature().getName();
        if (skips.contains(name)) return pjp.proceed();

        MethodSignature signature = CastUtils.cast(pjp.getSignature());
        Secured secured = signature.getMethod().getDeclaredAnnotation(Secured.class);
        if (Objects.nonNull(secured)) {
            HttpServletRequest request = ((ServletRequestAttributes) currentRequestAttributes()).getRequest();
            Long teacherId = getTeacherIdParam(request);
            String audCookie = Utils.safGet(WebUtils.getCookie(request, AUPD_CURRENT_ROLE), Cookie::getValue);
            String authToken = StringUtils.substringAfter(request.getHeader(AUTHORIZATION), "Bearer ");

            if (StringUtils.isNotBlank(authToken) && StringUtils.isNotBlank(audCookie)) {
                return authByAuthorizationHeader(pjp, secured, teacherId, audCookie);
            }
            String shareToken = Utils.safGet(WebUtils.getCookie(request, SHARE_TOKEN), Cookie::getValue);
            if (StringUtils.isNotBlank(shareToken)) {
                // Errors.E704.thr(HttpMethod.GET.matches(request.getMethod()));
                return authByCookie(pjp, secured, shareToken, teacherId);
            }

            throw Errors.E701.thr();
        }

        throw Errors.E704.thr();
    }

    private Long getTeacherIdParam(HttpServletRequest request) {
        String[] pathParamTeacherId = StringUtils.substringsBetween(
                request.getServletPath(), "teachers/", "/");
        Map<String, String[]> parameterMap = request.getParameterMap();
        Set<String> intersection = Sets.intersection(parameterMap.keySet(), teacherKey);

        String[] teacherId = ObjectUtils.firstNonNull(pathParamTeacherId,
                Utils.safetyGet(() -> parameterMap.get(getFirst(intersection, null))));//fixme

        return NumberUtils.createLong(ArrayUtils.get(teacherId, 0, null));
    }

    @SneakyThrows
    private Object authByAuthorizationHeader(ProceedingJoinPoint pjp, Secured secured,
                                             Long teacherId, String audCookie) {
        if (BooleanUtils.isFalse(secured.byPerson())) {
            throw Errors.E704.thr();
        }

        AccessTokenPayloadDto token = validateAndGet();
        Secured.GlobalRole role = checkRoleAndGet(secured, token, audCookie);

        if (Secured.GlobalRole.TEACHER.equals(role)) {
            Object target = pjp.getTarget();
            if (Objects.nonNull(teacherId)
                    && Objects.nonNull(target)
                    && CheckableController.class.isAssignableFrom(target.getClass())) {
                CheckableController casted = CastUtils.cast(target);
                check(casted.getClazz(), null, teacherId, Objects.requireNonNull(token));
            }

            validateSettings(target, null);
        }

        log.info("User Authorized");
        return pjp.proceed();
    }

    private Secured.GlobalRole checkRoleAndGet(Secured secured, AccessTokenPayloadDto token, String audCookie) {
        Secured.GlobalRole role = getRole(token, audCookie);

        EnumSet<Secured.GlobalRole> roles = Sets.newEnumSet(
                Arrays.asList(secured.globalRoles()), Secured.GlobalRole.class);
        Errors.E704.thr(roles.contains(role) || roles.contains(Secured.GlobalRole.ALL));

        return role;
    }

    private Secured.GlobalRole getRole(AccessTokenPayloadDto token, String aud) {
        String rls = Objects.requireNonNull(token).getRls();
        String audRole = Utils.getAudRole(aud);
        Secured.GlobalRole role = null;

        if (StringUtils.isNotBlank(audRole)) {
            String localRoles = StringUtils.substringBetween(rls, "{" + audRole + ":[", "]}");

            if (StringUtils.isNotBlank(localRoles)) {
                if (StringUtils.equalsAny(aud, audTeacher) && localRoles.contains(roleTeacher)) {
                    role = Secured.GlobalRole.TEACHER;
                } else if (StringUtils.equalsAny(aud, audAdmin)) {
                    if (localRoles.contains(roleReadAdmin)) {
                        role = Secured.GlobalRole.READ_ADMIN;
                    }
                    if (localRoles.contains(roleAdmin)) {
                        role = Secured.GlobalRole.ADMIN;
                    }
                }
            }
        }
        Errors.E704.thr(Objects.nonNull(role));

        return role;
    }

    @SneakyThrows
    private Object authByCookie(ProceedingJoinPoint pjp, Secured secured, String token, Long teacherId) {
        if (BooleanUtils.isFalse(secured.urlCookie())) {
            throw Errors.E704.thr();
        }

//        ZonedDateTime now = ZonedDateTime.now(UTC_ZONE);

        ShareLinkDTO.CookieDTO cookieDTO = crudService.getObjectMapper().readValue(
                new String(Base64Utils.decodeFromString(token)), ShareLinkDTO.CookieDTO.class);

        Map<String, Object> params = ImmutableMap.of(
                id, cookieDTO.getId(),
                url, cookieDTO.getUrl(),
                isActive, Boolean.TRUE
        );
        ShareLink shareLink = Utils.getWithError(
                //fixme mb create crud.findFirst
                () -> crudService.find(ShareLink.class, params, Pageable.unpaged()).iterator().next(),
                Errors.E704);
        crudService.getEntityManager().detach(shareLink);

//        Errors.E717.thr(now.isAfter(shareLink.getAccessStartDate())
//                && now.isBefore(shareLink.getAccessEndDate()));

        if (Objects.nonNull(teacherId)) {
            Errors.E704.thr(teacherId.equals(shareLink.getTeacher().getId()));
        }
        validateSettings(pjp.getTarget(), shareLink.getShareSettings());

        log.info("User Authorized");
        return pjp.proceed();
    }

    @SneakyThrows
    private void validateSettings(Object controller, JsonNode settings) {
        AdministratorSettings administratorSettings = Utils.safetyGet(
                () -> crudService.findByLatestDate(
                        AdministratorSettings.class, Collections.emptyMap()));
        if (Objects.isNull(administratorSettings)) {
            return;
        }
        crudService.getEntityManager().detach(administratorSettings);
        JsonNode jsonNode = administratorSettings.getSettings();
        Page<SectionRef> sectionRef = crudService.find(
                SectionRef.class, Collections.emptyMap(), Pageable.unpaged());
        SettingsController.putAllSettings(objectMapper, jsonNode, sectionRef);
        SettingsController.removePersonalInfo(jsonNode);

        JsonNode sections = jsonNode.get(SECTIONS);
        if (Objects.nonNull(settings)) {
            Set<Long> ids = new HashSet<>();
            for (JsonNode section : sections) {
                if (BooleanUtils.isFalse(Utils.safGet(section
                        .get(IS_VISIBLE), JsonNode::asBoolean))) {
                    ids.add(section.get("id").asLong());
                }
            }
            sections = settings.get(SECTIONS);
            for (JsonNode section : sections) {
                if (ids.contains(section.get("id").asLong())) {
                    ((ObjectNode) section).put(IS_VISIBLE, false);
                }
            }
        }

        String clazz = BeanUtils.getProperty(controller, "clazz");
        Set<String> entityCodes = Utils.extract(sectionRef.getContent(), SectionRef::getEntityCode);
        String targetEntityCode = StringUtils.substringAfterLast(clazz, Constants.DOT);
        boolean isGratitudeStudent = StringUtils.endsWith(clazz, GratitudeStudent.class.getSimpleName());
        if (!entityCodes.contains(targetEntityCode) && !isGratitudeStudent) {
            return;
        }

        boolean exist = false;
        for (JsonNode section : sections) {
            String entityCode = Utils.safGet(section.get(ENTITY_CODE), JsonNode::asText);
            if (entityCode.equals(targetEntityCode)
                    || (isGratitudeStudent && StringUtils.equals(entityCode, "WithStudent"))) {
                exist = section.get(IS_VISIBLE).asBoolean();
                break;
            }
        }
        Errors.E704.thr(exist);
    }

    @AfterReturning(value = "@annotation(secured)", returning = "result")
    public void secured(JoinPoint jp, Object result, Secured secured) {
//        Signature signature = pjp.getSignature();
        log.info("secured");
        if (!secured.byPerson() || Objects.isNull(result)) return;
        Class<?> resultClass = result.getClass();
        if (!TeacherEntity.class.isAssignableFrom(resultClass)) return;

        log.info("User Authorization");

        TeacherEntity teacherEntity = (TeacherEntity) result;
        Long id = Utils.safGet(teacherEntity, TeacherEntity::getId);
        Long teacherId = Utils.safGet(teacherEntity.getTeacher(), Teacher::getId);

        HttpServletRequest request = ((ServletRequestAttributes) currentRequestAttributes()).getRequest();
        AccessTokenPayloadDto token = Utils.safetyGet(this::validateAndGet);
        String audCookie = Utils.safGet(WebUtils.getCookie(request, AUPD_CURRENT_ROLE), Cookie::getValue);
        String shareToken = Utils.safGet(WebUtils.getCookie(request, SHARE_TOKEN), Cookie::getValue);
        if (Objects.nonNull(token)) {
            Secured.GlobalRole role = getRole(token, audCookie);
            if (Secured.GlobalRole.TEACHER.equals(role)) {
                check(resultClass, id, teacherId, token);
            }
        } else if (StringUtils.isNotBlank(shareToken)) {
            check(resultClass, id, teacherId, shareToken);
        }
        log.info("User Authorized");
    }

    private AccessTokenPayloadDto validateAndGet() {
        if (Objects.isNull(RequestContextHolder.getRequestAttributes())) {
            return null; // системный вызов
        }
        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String authToken = StringUtils.substringAfter(request.getHeader(AUTHORIZATION), "Bearer ");
        Errors.E701.thr(StringUtils.isNotBlank(authToken)); // АС1
        checkValidToken(authToken); // АС2

        AccessTokenPayloadDto tokenPayload = crudService.getTokenPayload();

        Long exp = createLong(tokenPayload.getExp());
        Long nbf = createLong(tokenPayload.getNbf());
        Long stf = tokenPayload.getStf();
        Instant now = Instant.now();

        Errors.E706.thr(now.minus(FIVE).isBefore(Instant.ofEpochSecond(exp))); // АС5
        Errors.E703.thr(now.plus(FIVE).isAfter(Instant.ofEpochSecond(nbf))); // АС3

        Errors.E711.thr(Objects.nonNull(stf)); // АС6
        Teacher teacher = crudService.findTeacherByStaffId(stf);
        Errors.E707.thr(Objects.nonNull(teacher), Teacher.class.getSimpleName(), stf); // АС6
        Errors.E707.thr(!teacher.getIsDeleted(), Teacher.class.getSimpleName(), stf); // АС6

        return tokenPayload;
    }

    private void check(Class<?> resultClass, Long id, Long teacherId, AccessTokenPayloadDto tokenPayload) {
        Teacher teacher = crudService.find(Teacher.class, Long.class,
                Teacher.Fields.staffId, tokenPayload.getStf().toString());
        checkTeacher(resultClass, id, teacherId, teacher);
    }

    @SneakyThrows
    private void check(Class<?> resultClass, Long id, Long teacherId, String shareToken) {
        Long teacherIdFromShareToken = crudService.getObjectMapper().readValue(
                new String(Base64Utils.decodeFromString(shareToken)), ShareLinkDTO.class)
                .getTeacherId();
        Teacher teacher = crudService.find(Teacher.class, teacherId);
        checkTeacher(resultClass, id, teacherIdFromShareToken, teacher);
    }

    private void checkTeacher(Class<?> resultClass, Long id, Long teacherId, Teacher teacher) {
        boolean check = Objects.nonNull(teacherId) && Objects.nonNull(teacher) && teacherId.equals(teacher.getId());
        if (Objects.nonNull(id)) {
            Errors.E712.thr(check, resultClass.getSimpleName(), id);
        }
        Errors.E711.thr(check);
    }

    @SneakyThrows
    private void checkValidToken(String token) {
        final byte[] keyByte = IOUtils.toByteArray(
                Objects.requireNonNull(getClass().getClassLoader().getResourceAsStream(keyName)));
        final X509EncodedKeySpec pubKeySpec = new X509EncodedKeySpec(keyByte);
        final KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        final PublicKey publicKey = keyFactory.generatePublic(pubKeySpec);


        final JWSVerifier verifier = new RSASSAVerifier((RSAPublicKey) publicKey);
        try {
            SignedJWT signedJWT = SignedJWT.parse(token);
            Errors.E702.thr(signedJWT.verify(verifier));
        } catch (Exception ex) {
            log.error("checkValidToken", ex);
            Errors.E702.thr(Boolean.FALSE);
        }
    }
}
