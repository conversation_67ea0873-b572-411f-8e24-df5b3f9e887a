package tech.clink.mesh.porteacher.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.util.ClassUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Iterables;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.validator.internal.util.ReflectionHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.util.CastUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import tech.clink.mesh.porteacher.configuration.datasource.ReadOnlyHolder;
import tech.clink.mesh.porteacher.model.*;
import tech.clink.mesh.porteacher.model.common.*;
import tech.clink.mesh.porteacher.model.dto.DeletedDTO;
import tech.clink.mesh.porteacher.model.dto.ShareLinkDTO;
import tech.clink.mesh.porteacher.model.dto.aupd.AccessTokenPayloadDto;
import tech.clink.mesh.porteacher.model.ref.RefEntity;
import tech.clink.mesh.porteacher.model.ref.RoleTypeRef;
import tech.clink.mesh.porteacher.service.kafka.dto.NsiEntity;
import tech.clink.mesh.porteacher.util.Constants;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.Utils;
import tech.clink.mesh.porteacher.util.validation.Secured;

import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.metamodel.Attribute;
import javax.persistence.metamodel.EntityType;
import javax.persistence.metamodel.Type;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.time.LocalDateTime;
import java.time.temporal.Temporal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.google.common.collect.ImmutableMap.of;
import static com.google.common.net.HttpHeaders.AUTHORIZATION;
import static com.google.common.net.HttpHeaders.COOKIE;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;
import static org.apache.commons.lang3.StringUtils.*;
import static org.springframework.web.context.request.RequestContextHolder.currentRequestAttributes;
import static tech.clink.mesh.porteacher.model.common.Documentable.Fields.createdBy;
import static tech.clink.mesh.porteacher.util.CollectionUtils.extract;
import static tech.clink.mesh.porteacher.util.Constants.*;

@Slf4j
@Service
@Transactional
public class CrudService {
    private final ReadOnlyHolder readOnlyHolder;
    @Getter
    private final ObjectMapper objectMapper;
    @Getter
    private final EntityManager entityManager;
    private final Map<String, Class<?>> className2Class2Identity;

    @Value("${roles.teacherId}")
    private String roleTeacher;
    @Value("${roles.adminId}")
    private String roleAdmin;
    @Getter
    @Value("${auds.teacher}")
    private String[] audTeacher;

    private final Map<Class<?>, Function<String, Object>> converter = ImmutableMap.of(
            String.class, Object::toString,
            Long.class, NumberUtils::createLong,
            Boolean.class, Boolean::parseBoolean,
            Integer.class, NumberUtils::createInteger
    );

    public CrudService(@Autowired(required = false) ReadOnlyHolder readOnlyHolder,
                       ObjectMapper objectMapper, EntityManager entityManager) {
        this.readOnlyHolder = readOnlyHolder;
        this.objectMapper = objectMapper;
        this.entityManager = entityManager;
        className2Class2Identity = Utils.index(entityManager.getMetamodel().getEntities(), t ->
                removeEnd(uncapitalize(t.getJavaType().getSimpleName()), "Ref"), Type::getJavaType);
    }

    public <T, ID> T findNullable(Class<T> type, ID id) {
        if (Objects.isNull(id)) return null;
        return Optional.ofNullable(entityManager.find(type, id)).orElse(null);
    }

    @Secured
    @SuppressWarnings("unchecked")
    public <T> T find(String type, Object id) {
        Class<?> clazz = className2Class2Identity.get(type);
        return (T) find(clazz, NumberUtils.createLong(id.toString()));
    }

    @Secured
    public <T, ID> T find(Class<T> type, ID id) {
        Errors.E707.thr(Objects.nonNull(id), type, NULL);
        return Optional.ofNullable(entityManager.find(type, id))
                .orElseThrow(() -> Errors.E707.thr(type.getSimpleName(), id));
    }

    public <T> T findByLatestDate(Class<T> type, Map<String, ?> params) {
        return entityManager.createQuery(createJpqlQueryForFindByLatestDate(type, params),
                type).getSingleResult();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public <T> T findByLatestDateInNewTransaction(Class<T> type, Map<String, ?> params) {
        return entityManager.createQuery(createJpqlQueryForFindByLatestDate(type, params),
                type).getSingleResult();
    }

    private <T> String createJpqlQueryForFindByLatestDate(Class<T> type, Map<String, ?> params) {
        StringBuilder jpql = new StringBuilder("select a from " + type.getSimpleName() +
                " a where a.createdDate = (select max(a.createdDate) from " + type.getSimpleName() + " a");
        if (MapUtils.isNotEmpty(params)) {
            jpql.append(" where");
            for (String param : params.keySet()) {
                jpql.append(" a.").append(param).append(" = ").append(params.get(param)).append(" and");
            }
            jpql.replace(jpql.length() - StringUtils.length(" and"), jpql.length(), EMPTY);
        }
        jpql.append(")");

        return jpql.toString();
    }

    public <T> List<T> findHistoryAdminSettingsByDate(
            Class<T> type, Long sectionId, Map<String, Object> params, String dataSort) {

        TypedQuery<T> typedQuery =
                entityManager.createQuery(VISIBILITY_SETTING_HISTORY_QUERY + dataSort, type);

        typedQuery.setParameter("sectionId", sectionId);
        return addParamsToTypedQuery(typedQuery, params).getResultList();
    }

    public <T> List<T> findHistoryAdminSettingsByDate(
            Class<T> type, Map<String, Object> params, String dataSort) {

        TypedQuery<T> typedQuery =
                entityManager.createQuery(VISIBILITY_SETTING_HISTORY_QUERY_ALL_SECTIONS + dataSort, type);

        return addParamsToTypedQuery(typedQuery, params).getResultList();
    }

    private <T> TypedQuery<T> addParamsToTypedQuery(TypedQuery<T> typedQuery, Map<String, Object> params) {
        typedQuery.setParameter("dateFrom", params.get("dateFrom"));
        typedQuery.setParameter("dateTo", params.get("dateTo"));
        typedQuery.setFirstResult((int) params.get("offset"));
        typedQuery.setMaxResults((int) params.get("limit"));
        return typedQuery;
    }

    public <ID, T extends ReachableEntity> List<T> find(Class<T> type, Set<ID> id) {
        if (CollectionUtils.isEmpty(id)) return Collections.emptyList();
        String jpql = "select e from " + type.getSimpleName() + " e where e.id in ?1 ";
        if (Deletable.class.isAssignableFrom(type)) {
            jpql += " and e.isDeleted = false ";
        }
        if (Award.class.isAssignableFrom(type)) {
            jpql += " and e.entityCode is null and e.entityId is null ";
        }
        TypedQuery<T> tTypedQuery = entityManager.createQuery(jpql, type).setParameter(1, id);
        List<T> resultList = ReadOnlyHolder.read(readOnlyHolder, tTypedQuery::getResultList);
        Set<Object> diff = Sets.symmetricDifference(id, extract(resultList, ReachableEntity::getId));

        if (!Award.class.isAssignableFrom(type) && !diff.isEmpty()) {
            throw Errors.E707.thr(type.getSimpleName(), diff);
        }
        return resultList;
    }

    public <T> List<T> findAll(Class<T> clazz, String field, Object value) {
        Pair<TypedQuery<T>, TypedQuery<Long>> query = createQuery(clazz,
                Collections.singletonMap(field, value), Pageable.unpaged());
        return ReadOnlyHolder.read(readOnlyHolder, () -> query.getLeft().getResultList());
    }

    @Secured
    @Nullable
    public <T> T find(Class<T> clazz, Class<?> type, String field, Object value) {
        return ReadOnlyHolder.read(readOnlyHolder, () ->
                Iterables.getFirst(findOne(clazz, field, type,
                        String.valueOf(value)), null)
        );
    }

    public <T> List<T> findOne(Class<T> clazz, String field, Class<?> type, String value) {
        return entityManager.createQuery("select e from "
                + clazz.getSimpleName() + " e where " + field + " = ?1 ", clazz)
                .setParameter(1, converter.get(type).apply(value))
                .getResultList();
    }

    public <T> List<T> findAllRef(Class<T> clazz) {
        Errors.E711.thr(Objects.nonNull(clazz));
        return CastUtils.cast(find(clazz, Collections.emptyMap(), Pageable.unpaged()).getContent());
    }

    public List<? extends RefEntity> findAllRef(String clazz, Map<String, Object> map) {
        Class<?> clazz2 = className2Class2Identity.get(uncapitalize(clazz));
        Errors.E711.thr(Objects.nonNull(clazz2));
        if (Deletable.class.isAssignableFrom(clazz2)) {
            Utils.putAdditionalParamsIfNecessary(map, Map.of(Deletable.FIELD, Boolean.FALSE));
        }
        return CastUtils.cast(find(clazz2, map, Pageable.unpaged()).getContent());
    }

    public <E extends Entitiable> List<E> findAllEntitiesByCodeAndId(String code,
                                                                     Long entityId,
                                                                     Class<E> clazz) {
        if (isEmpty(code)) return null;

        return find(clazz, of(ENTITY_CODE, code, ENTITY_ID, entityId), Pageable.unpaged()).getContent();
    }

    public <E extends Entitiable> List<E> findNotDeletedEntitiesByCodeAndId(String code,
                                                                            Long entityId,
                                                                            Class<E> clazz) {
        if (isEmpty(code)) return null;

        return find(clazz, of(ENTITY_CODE, code, ENTITY_ID, entityId, Deletable.FIELD, Boolean.FALSE),
                Pageable.unpaged()).getContent();
    }

    @SneakyThrows
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public NsiEntity findByNsiId(String className, Long nsiId) {
        if (Objects.isNull(nsiId)) return null;
        Class<? extends NsiEntity> clazz = CastUtils.cast(Class.forName(PACK + className));
        return find(clazz, Long.class, NsiEntity.FIELD, nsiId.toString());
    }

    public Teacher findTeacherByStaffId(Object staffId) {
        return find(Teacher.class, Long.class, Teacher.Fields.staffId, staffId.toString());
    }

    @Secured
    public <T> List<T> find(String type, Map<String, ?> map) {
        Class<?> clazz = className2Class2Identity.get(type);
        Errors.E707.thr(Objects.nonNull(clazz), type);
        return CastUtils.cast(find(clazz, map, Pageable.unpaged()).getContent());
    }

    public <T> Page<T> find(Class<T> clazz, Map<String, ?> map, Pageable pageable) {
        Pair<TypedQuery<T>, TypedQuery<Long>> pair = createQuery(clazz, map, pageable);
        return ReadOnlyHolder.read(readOnlyHolder, () ->
                new PageImpl<>(pair.getLeft().getResultList(),
                        pageable, pair.getRight().getSingleResult()));
    }

    public <T> Long count(Class<T> clazz, Map<String, ?> map) {
        Pair<TypedQuery<T>, TypedQuery<Long>> pair = createQuery(clazz, map, Pageable.unpaged());
        return ReadOnlyHolder.read(readOnlyHolder, () -> pair.getRight().getSingleResult());
    }

    public <T> Map<String, Long> countAwardable(Class<T> clazz, Map<String, Object> map) {
        Long total = count(clazz, map);
        map.put(Constants.WITH_AWARD, Boolean.TRUE);
        Long withAward = count(clazz, map);

        return ImmutableMap.of("total", total,
                uncapitalize(clazz.getSimpleName()) + "s" + capitalize(WITH_AWARD), withAward);
    }

    public Map<String, Long> groupCount(Class<?> clazz, Map<String, Object> map, Function<String, String> select2Count) {
        Pair<Query, TypedQuery<Long>> pair = createQuery(clazz, map, Pageable.unpaged(), select2Count);
        List<?> code2count = ReadOnlyHolder.read(readOnlyHolder,
                () -> pair.getLeft().getResultList());

        Map<String, Long> maps = code2count.stream().map(ObjectUtils::toObjectArray)
                .collect(toMap(t -> String.valueOf(t[0]), t -> CastUtils.cast(t[1])));
        maps.put("all", maps.values().stream().mapToLong(v -> v).sum());

        return maps;
    }

    public Map<String, Long> groupCountGratitude(Class<?> clazz, Map<String, Object> map) {
        return this.groupCount(clazz, map,
                t -> replace(t, AWARD_SELECT, GRAD_GROUP_COUNT) + GRAD_GROUP);
    }

    public Map<String, Long> groupCountAward(Class<?> clazz, Map<String, Object> map) {
        Map<String, Long> mapGroupCount = this.groupCount(clazz, map,
                t -> replace(t, AWARD_SELECT, AWARD_GROUP_COUNT) + AWARD_GROUP);
        Map<String, Long> longMap = new HashMap<>();

        longMap.put("personal", mapGroupCount.entrySet().stream()
                .filter(entry -> entry.getKey().contains("Муниципальная награда")
                        || entry.getKey().contains("Государственная награда")
                        || entry.getKey().contains("Другое"))
                .mapToLong(Map.Entry::getValue).sum());
        longMap.put("event", mapGroupCount.entrySet().stream()
                .filter(entry -> entry.getKey().contains("Олимпиада") || entry.getKey().contains("Конкурс"))
                .mapToLong(Map.Entry::getValue).sum());
        longMap.put("total", mapGroupCount.get("all"));

        return longMap;
    }

    public Map<String, Long> groupCountDiagnostic(Class<?> clazz, Map<String, Object> map) {
        Map<String, Long> mapGroupCount = this.groupCount(clazz, map,
                t -> replace(t, AWARD_SELECT, DIAGNOSTIC_GROUP_COUNT) + DIAGNOSTIC_GROUP);
        Map<String, Long> longMap = new HashMap<>();

        longMap.put("belowBase", mapGroupCount.entrySet().stream()
                .filter(entry -> entry.getKey().contains("Ниже базового"))
                .mapToLong(Map.Entry::getValue).sum());
        longMap.put("base", mapGroupCount.entrySet().stream()
                .filter(entry -> entry.getKey().contains("Базовый"))
                .mapToLong(Map.Entry::getValue).sum());
        longMap.put("elevated", mapGroupCount.entrySet().stream()
                .filter(entry -> entry.getKey().contains("Повышенный"))
                .mapToLong(Map.Entry::getValue).sum());
        longMap.put("high", mapGroupCount.entrySet().stream()
                .filter(entry -> entry.getKey().contains("Высокий"))
                .mapToLong(Map.Entry::getValue).sum());
        longMap.put("total", mapGroupCount.get("all"));

        return longMap;
    }

    public <T> Pair<TypedQuery<T>, TypedQuery<Long>> createQuery(Class<T> clazz, Map<String, ?> map, Pageable pageable) {
        return CastUtils.cast(createQuery(clazz, map, pageable, Function.identity()));
    }

    public <T> Pair<Query, TypedQuery<Long>> createQuery(Class<T> clazz, Map<String, ?> map,
                                                         Pageable pageable, Function<String, String> q) {
        Map<String, Object> mutableMap = Maps.newHashMap(map);
        StringBuilder jpql = new StringBuilder();
        jpql.append("select e from ").append(clazz.getSimpleName()).append(" e ");
        jpql.append("where e.id is not null ");

        if (Awardable.class.isAssignableFrom(clazz) &&
                BooleanUtils.isTrue(Boolean.valueOf(Utils.safetyGet(() -> mutableMap.get(WITH_AWARD).toString())))) {
            jpql.append("and 0 < (select count(*) from Award a where a.entityCode = '")
                    .append(Utils.camel2underscore(clazz.getSimpleName()))
                    .append("' and a.entityId = e.id and a.isDeleted = false) ");
        }
        mutableMap.remove(WITH_AWARD);

        mutableMap.entrySet().forEach(entry -> entry.setValue(Objects.isNull(entry.getValue()) ?
                NULL : entry.getValue()));

        Map<String, Object> params = new ConcurrentHashMap<>(mutableMap);
        Map<String, Object> paramS = Maps.newHashMap();
        Utils.clear(params);

        for (String key : params.keySet()) {
            if (key.contains(DOT)) {
                classifier(mutableMap, jpql, params, paramS, key);
            } else {
                classifier(mutableMap, jpql, params, paramS, key, key, mutableMap.get(key), clazz);
            }
        }
        paramS.putAll(params);

        if (pageable.getSort().isSorted()) {
            jpql.append(" order by ");
            pageable.getSort().get().forEach(o -> {
                jpql.append("e.").append(o.getProperty())
                        .append(SPACE)
                        .append(o.getDirection().name())
                        .append(", ");
            });
            jpql.append(" e.id desc ");
        }

        Query query;
        String pql = q.apply(jpql.toString());
        if (!contains(pql, " group by")) {//fixme costyl
            if (Function.identity().equals(q)) {
                query = entityManager.createQuery(pql, clazz);
            } else {
                query = entityManager.createQuery(pql, Tuple.class);
            }
        } else {
            query = entityManager.createQuery(pql);
        }
        TypedQuery<Long> count = entityManager.createQuery(substringBefore(replace(
                jpql.toString(), "select e", "select count(e)"), "order by"), Long.class);
        for (Map.Entry<String, Object> entry : paramS.entrySet()) {
            query.setParameter(entry.getKey(), entry.getValue());
            count.setParameter(entry.getKey(), entry.getValue());
        }
        if (pageable.isPaged()) {
            query = query.setFirstResult(Math.toIntExact(pageable.getOffset()))
                    .setMaxResults(pageable.getPageSize());
        }
        return Pair.of(query, count);
    }

    @SneakyThrows
    private void classifier(Map<String, Object> map, StringBuilder jpql,
                            Map<String, Object> params,
                            Map<String, Object> paramS,
                            String key) {
        String[] split = split(key, DOT);
        String className = capitalize(split[0]);
        String pack = endsWith(className, "Ref") ? "ref." : "";
        String packageClass = PACK + pack + className;
        classifier(map, jpql, params, paramS, key, split[1], params.get(key),
                Class.forName(packageClass));
    }

    private void classifier(Map<String, Object> map, StringBuilder jpql,
                            Map<String, Object> params,
                            Map<String, Object> paramS,
                            String key, String newKey,
                            Object value,
                            Class<?> clazz) {
        Class<?> aClass = getFieldDescriptionMap(clazz, newKey);
        Errors.E711.thr(Objects.nonNull(aClass));
        String param = uncapitalize(clazz.getSimpleName()) + capitalize(newKey);

        if (NULL.equals(value)) { //fixme costyl?
            jpql.append(" and e.").append(key).append(" is null");
            params.remove(key);

            return;
        }

        if (String.class.equals(aClass)) {
            jpql.append(" and lower(e.").append(key).append(") like :").append(param);
            params.replace(key, Utils.lowerLike(value));
        }
        if (ReachableEntity.class.isAssignableFrom(aClass)) {
            jpql.append(" and (e.").append(StringUtils // todo check
                    .uncapitalize(aClass.getSimpleName())).append(".id) = :").append(param);
            params.replace(key, Utils.lowerLike(map.get(key)));
        }
        if (Temporal.class.isAssignableFrom(aClass)) {
            String field = Constants.between(key);
            String f = field + BETWEEN[0];
            String t = field + BETWEEN[1];

            String dateParam = Constants.between(param);
            String paramFrom = dateParam + BETWEEN[0];
            String paramTo = dateParam + BETWEEN[1];

            if (paramS.containsKey(paramFrom) || paramS.containsKey(paramTo)) return;
            boolean isLdt = aClass.equals(LocalDateTime.class);
            Temporal from = Utils.date(params.remove(f), true, aClass);
            Temporal to = Utils.date(params.remove(t), false, aClass);
            Temporal middle = Utils.date(params.remove(field), null, aClass);
            if (Objects.nonNull(middle)) {
                from = to = middle;
            }

            jpql.append(" and e.").append(field).append(" between :")
                    .append(paramFrom).append(" and :").append(paramTo);

            paramS.put(paramFrom, from);
            paramS.put(paramTo, to);
            params.remove(f);
            params.remove(t);

            return;
        }
        if (Number.class.isAssignableFrom(aClass)) {
            if (!ReflectionHelper.isIterable(value.getClass())) {
                value = Collections.singletonList(value);
            }
            jpql.append(" and e.").append(key).append(" in (")
                    .append(Utils.join((Collection<?>) value)).append(") ");
            params.remove(key);

            return;
        }
        if (Boolean.class.isAssignableFrom(aClass)) {
            jpql.append(" and e.").append(key).append(" = :").append(param);
            params.replace(key, converter.get(Boolean.class).apply(map.get(key).toString()));
        }
        if (ClassUtil.isEnumType(aClass)) {
            jpql.append(" and e.").append(key).append(" = :").append(param);
            params.replace(key, EnumUtils.getEnum(CastUtils.cast(aClass), params.get(key).toString()));
        }
        paramS.put(param, params.remove(key));
    }

    public <E extends ReachableDTO> void persist(Iterable<E> reachable) {
        if (Iterables.isEmpty(reachable)) return;
        reachable.forEach(this::merge);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void delete(Map<String, Object> reachables, Deletable object) {
        readOnlyHolder.set(Boolean.FALSE);
        if (MapUtils.isEmpty(reachables)) return;
        log.info("Started delete deletable {}", object.getId());
        object.setIsDeleted(Boolean.TRUE);
        entityManager.merge(object);
        log.info("Deleted deletable {}", object.getId());

        for (String key : reachables.keySet()) {
            Object value = reachables.get(key);
            if (NsiEntity.class.isAssignableFrom(value.getClass())) {
                NsiEntity casted = (NsiEntity) value;
                NsiEntity o = findByNsiId(key, casted.getNsiId());
                if (Objects.nonNull(o)) {
                    delete(o);
                }
            }
        }
        log.info("Deleted deletable {}", object.getId());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void persist(Map<String, Object> map, Deletable object, Set<Class<?>> needed) {
        log.info("Start Saved deletable {}", object.getClass().getSimpleName());
        ReachableDTO merge = this.merge(object);
        log.info("Merged deletable {}", merge.getId());
        if (MapUtils.isEmpty(map)) return;

        LinkedList<NsiEntity> objects = new LinkedList<>();
        for (String clazz : map.keySet()) {
            Object value = map.get(clazz);
            if (NsiEntity.class.isAssignableFrom(value.getClass())) {
                NsiEntity casted = (NsiEntity) value;
                NsiEntity nsi = findByNsiId(clazz, casted.getNsiId());
                if (Objects.nonNull(nsi)) {
                    Utils.copyNonNullProperties(value, nsi);
                } else {
                    nsi = casted;
                }
                nsi.reach(this);
                nsi.setDeleted(merge);
                objects.add(nsi);
            }
        }


        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        Set<Class<?>> valid = new HashSet<>();
        for (NsiEntity o : objects) {
            Set<ConstraintViolation<NsiEntity>> validate = validator.validate(o);
            if (needed.contains(o.getClass())) {
                String collect = validate.stream()
                        .map(s -> s.getPropertyPath().toString())
                        .collect(Collectors.joining(", "));
                Errors.E708.thr(validate.isEmpty(),
                        o.getClass().getSimpleName() + ": " + collect);
                valid.add(o.getClass());
            } else {
                if (validate.isEmpty()) {
                    valid.add(o.getClass());
                }
            }
        }
        objects.stream()
                .filter(o -> valid.contains(o.getClass()))
                .forEach(this::merge);
        log.info("End Saved deletable {}", merge.getId());
    }

    @Secured
    public <E extends ReachableEntity, T extends ReachableDTO> E save(T reachable) {
        Class<? extends ReachableDTO> clazz = reachable.getClass();
        readOnlyHolder.set(Boolean.FALSE);

        // 2
        List<DocumentStorage> documentStorageList = Collections.emptyList();
        if (Documentable.class.isAssignableFrom(clazz)) {
            Documentable casted = (Documentable) reachable;
            Map<Boolean, Set<Long>> collect = getCollectSaveOrDelete(casted.getDocs());
            Set<Long> deletedDocumentIds = SetUtils.emptyIfNull(collect.get(Boolean.TRUE));
            documentStorageList = find(DocumentStorage.class, collect.get(Boolean.FALSE)).stream()
                    .filter(document -> !deletedDocumentIds.contains(document.getId()))
                    .collect(Collectors.toList());

            Errors.E715.thr(documentStorageList.stream()
                    .noneMatch(doc -> isNotBlank(doc.getEntityCode()) || Objects.nonNull(doc.getEntityId())));

            customDelete(deletedDocumentIds, DocumentStorage.class);
            casted.setDocs(documentStorageList);
        }

        List<Award> awardsList = Collections.emptyList();
        if (Awardable.class.isAssignableFrom(clazz)) {
            Awardable casted = (Awardable) reachable;
            Map<Boolean, Set<Long>> collect = getCollectSaveOrDelete(casted.getAws());
            awardsList = find(Award.class, collect.get(Boolean.FALSE));

            customDelete(collect.get(Boolean.TRUE), Award.class);
            casted.setAws(awardsList);
        }

        reachable.validateOnCreate(this);
        ReachableDTO reach = reachable.reach(this);
        entityManager.persist(reach);

        if (Event.class.isAssignableFrom(clazz)) {
            persistEventRoles(CastUtils.cast(reach));
        }

        // 2.1
        String code = Utils.camel2underscore(clazz.getSimpleName());
        for (DocumentStorage doc : documentStorageList) {
            doc.setEntityId(reach.getId());
            doc.setEntityCode(code);
        }
        for (Award award : awardsList) {
            award.setEntityId(reach.getId());
            award.setEntityCode(code);
        }
        this.persist(documentStorageList);

        reach.reachTransient(this);
        return CastUtils.cast(reach);
    }

    private void persistEventRoles(Event event) {
        Optional.ofNullable(event.getRoles()).orElse(Collections.emptyList()).stream()
                .peek(role -> role.reach(this))
                .peek(role -> role.setEventId(event.getId()))
                .forEach(role -> {
                    if (Boolean.FALSE.equals(role.getIsDeleted())) {
                        entityManager.persist(role);
                    } else {
                        delete(role.getId(), role.getClass());
                    }
                });
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public LogAction logAction(LogAction logAction) {
        return entityManager.merge(logAction);
    }

    public <E extends ReachableDTO, T extends ReachableDTO> E merge(T reachable) {
        log.info("Merge entity {}", reachable.getClass().getSimpleName());
        ReachableDTO reach = reachable.reach(this);
        readOnlyHolder.set(Boolean.FALSE);
        ReachableDTO merge = entityManager.merge(reach);
        log.info("Merged entity {} {}", merge.getClass().getSimpleName(), merge.getId());
        return CastUtils.cast(merge);
    }

    public <E extends ReachableEntity, T extends ReachableDTO> E update(Class<T> clazz, Class<?> type,
                                                                        String field, Object value, T object) {
        List<T> one = findOne(clazz, field, type, String.valueOf(value));
        Errors.E707.thr(!one.isEmpty(), value);
        return update(one.iterator().next(), object);
    }

    @Deprecated
    public <E extends ReachableEntity, T extends ReachableDTO> E update(Long id, T object) {
        return update(find(object.getClass(), id), object);
    }

    @Secured
    public <E extends ReachableEntity, T extends ReachableDTO> E update(T origin, T object) {
        if (origin instanceof Deletable) {
            Deletable d = (Deletable) origin;
            Errors.E713.thr(!d.getIsDeleted(), d.getClass().getSimpleName(), d.getId());
        }

        readOnlyHolder.set(Boolean.FALSE);
        if (origin instanceof Award) {
            return CastUtils.cast(updateAwards(CastUtils.cast(origin), CastUtils.cast(object)));
        }
        if (origin instanceof Event) {
            updateEvents(CastUtils.cast(origin), CastUtils.cast(object));
        }
        if (origin instanceof AdditionalEducation) {
            updateAdditionalEducation(CastUtils.cast(origin), CastUtils.cast(object));
        }

        Long id = origin.getId();
        object.validateOnUpdate(this, origin);
        ReachableDTO reach = object.reachOnUpdate(this);
        Class<? extends ReachableDTO> clazz = reach.getClass();
        String code = Utils.camel2underscore(clazz.getSimpleName());

        if (reach instanceof Documentable) {
            updateAwardsAndDocs(id, CastUtils.cast(reach), code);
        }

        Utils.copyNonNullProperties(reach, origin, createdBy);
        entityManager.persist(origin);

        if (Event.class.isAssignableFrom(clazz)) {
            persistEventRoles(CastUtils.cast(origin));
        }

        return CastUtils.cast(origin);
    }

    private <T extends ReachableDTO> void updateAdditionalEducation(AdditionalEducation origin, AdditionalEducation object) {
        if (!getTokenPayload().getRls().contains(roleAdmin)) { //fixme duplicate with part of UpdateEvent
            if (NumberUtils.LONG_ZERO.equals(origin.getCreatedBy())) {
                Errors.E714.thr(NumberUtils.LONG_ONE.equals(Utils.countNonNullProperties(object)) &&
                        isNotEmpty(object.getDocs()));
            }
        }
        Errors.E711.thr(StringUtils.isBlank(object.getOrganizationName())
                || Objects.isNull(object.getMap().get("organizationCodeId")));
        if (StringUtils.isNotBlank(object.getOrganizationName())) {
            origin.setOrganizationCodeRef(null);
        } else if (Objects.nonNull(object.getMap().get("organizationCodeId"))) {
            origin.setOrganizationName(null);
        }

        origin.reachTransient(this);
    }

    public <E extends ReachableEntity> Award updateAwards(Award origin, Award object) {
        origin.forUpdate(object, this);
        String code = Utils.camel2underscore(Award.class.getSimpleName());

        readOnlyHolder.set(Boolean.FALSE);
        entityManager.persist(updateAwardsAndDocs(origin.getId(), origin, code));

        return CastUtils.cast(origin);
    }

    public <E extends ReachableEntity> void updateEvents(Event origin, Event object) {
        Collection<RoleAtEvent> objectRoles =
                org.apache.commons.collections4.CollectionUtils.emptyIfNull(object.getRoles());
        for (RoleAtEvent originRole : origin.getRoles()) {
            for (RoleAtEvent role : objectRoles) {
                if (Objects.equals(originRole.getId(), role.getId())) {
                    originRole.setIsDeleted(role.getIsDeleted());
                }
            }
        }
        origin.reachTransient(this);

        if (!getTokenPayload().getRls().contains(roleAdmin)) {
            if (NumberUtils.LONG_ZERO.equals(origin.getCreatedBy())) {
                Errors.E714.thr(NumberUtils.LONG_ONE.equals(Utils.countNonNullProperties(object)) &&
                        isNotEmpty(object.getDocs()));
            }
        }

        List<RoleAtEvent> originRoles = origin.getRoles();
        originRoles.stream()
                .filter(originRole -> Boolean.TRUE.equals(originRole.getIsDeleted()))
                .forEach(this::delete);
        objectRoles = objectRoles.stream()
                .peek(role -> role.reach(this))
                .collect(Collectors.toList());
        if (isNotEmpty(originRoles) && isNotEmpty(objectRoles)) {
            Set<RoleTypeRef> originRoleTypes = originRoles.stream()
                    .map(RoleAtEvent::getRoleTypeRef)
                    .collect(Collectors.toSet());

            object.setRoles(objectRoles.stream()
                    .filter(role -> Boolean.FALSE.equals(role.getIsDeleted()))
                    .filter(role -> !originRoleTypes.contains(role.getRoleTypeRef()))
                    .collect(Collectors.toList()));
        }
    }

    // some entities with docs/awards may not be Entitiable, poka hz
    private <E extends ReachableEntity & Entitiable> E updateAwardsAndDocs(Long id, E origin, String code) {
        if (origin instanceof Awardable) {
            Awardable casted = (Awardable) origin;
            if (isNotEmpty(casted.getAws()))
                casted.setAws(this.getAndDeleteEntityWithCodeId(id,
                        origin, code, DeletedDTO.AWARDS, Award.class));
            else casted.setAws(this.findAllEntitiesByCodeAndId(code, id, Award.class));
        }
        if (origin instanceof Documentable) {
            Documentable casted = (Documentable) origin;
            if (!CollectionUtils.isEmpty(casted.getDocs())) {
                Map<Boolean, Set<Long>> collect = getCollectSaveOrDelete(casted.getDocs());
                List<DocumentStorage> documentStorageList = find(DocumentStorage.class, collect.get(Boolean.FALSE));

                Errors.E715.thr(documentStorageList.stream()
                        .noneMatch(doc -> Objects.nonNull(doc.getEntityCode()) || Objects.nonNull(doc.getEntityId())));

                casted.setDocs(this.getAndDeleteEntityWithCodeId(id,
                        origin, code, DeletedDTO.DOCUMENT, DocumentStorage.class));
            } else {
                casted.setDocs(this.findAllEntitiesByCodeAndId(code, id, DocumentStorage.class));
                // setDocuments() and setAwards() -> how extract method?
            }
        }

        return origin;
    }

    /**
     * Метод сохраняет или удаляет {@link Entitiable} сущности у origin.
     *
     * @param id             сущности origin
     * @param origin         сущность, у которой есть поля, имеющие entityCode и entityId (например, {@link DocumentStorage})
     * @param code           код сущности origin
     * @param deletedDtoType тип сущности, которая может быть сохранена или удалена,
     *                       в зависимости от параметра {@link DeletedDTO#getIsDeleted()}
     * @param clazz          класс сущности, имеющий поля entityCode и entityId (например, {@link DocumentStorage})
     * @param <E>            класс сущности origin
     * @param <T>            класс clazz сохраняемых/удаляемых сущностей
     * @return список всех сущностей класса clazz, связанных с сущностью origin
     */
    @Deprecated // slozhno
    private <E extends ReachableEntity, T extends ReachableEntity & Entitiable>
    List<T> getAndDeleteEntityWithCodeId(Long id, E origin, String code,
                                         String deletedDtoType,
                                         Class<T> clazz) {
        Documentable cast = (Documentable) origin;

        Map<Boolean, Set<Long>> collect = DeletedDTO.AWARDS.equals(deletedDtoType) ?
                getCollectSaveOrDelete(((Awardable) cast).getAws())
                : getCollectSaveOrDelete(cast.getDocs());

        Set<Long> empty = Collections.emptySet();
        Set<Long> entitiesSave = collect.getOrDefault(Boolean.FALSE, empty);
        Set<Long> entitiesDelete = collect.getOrDefault(Boolean.TRUE, empty);

        AccessTokenPayloadDto tokenPayload;
        if (isNotEmpty(entitiesDelete) && containsNone((tokenPayload = getTokenPayload()).getRls(), roleAdmin)) {
            if (contains(tokenPayload.getRls(), roleTeacher))
                Errors.E714.thr((new ArrayList<>(this.find(clazz, entitiesDelete)))
                        .stream().allMatch(e -> ((Documentable) e).getCreatedBy().equals(tokenPayload.getStf())));
            else throw Errors.E714.thr();
        }

        List<T> saveEntitiesList = new ArrayList<>(this.find(clazz, entitiesSave));
        List<T> allEntities = List.copyOf(emptyIfNull(this.findAllEntitiesByCodeAndId(code, id, clazz)));

        allEntities.stream()
                .filter(d -> !saveEntitiesList.contains(d) && !entitiesDelete.contains(d.getId()))
                .forEach(saveEntitiesList::add);
        // 2.1
        for (T ent : saveEntitiesList) {
            ent.setEntityId(id);
            ent.setEntityCode(Utils.camel2underscore(code));
        }

        this.customDelete(entitiesDelete, clazz);

        return saveEntitiesList;
    }

    private Map<Boolean, Set<Long>> getCollectSaveOrDelete(List<DeletedDTO> entityDTO) {
        return emptyIfNull(entityDTO).stream() // old code is twice iterated
                .collect(Collectors.groupingBy(DeletedDTO::getIsDeleted,
                        Collectors.mapping(DeletedDTO::getId, Collectors.toSet())));
    }

    private <E extends ReachableEntity & Entitiable> void customDelete(Set<Long> entitiesDelete, Class<E> clazz) {
        if (CollectionUtils.isEmpty(entitiesDelete)) return;
        if (clazz.isAssignableFrom(DocumentStorage.class)) {
            entitiesDelete.forEach(d -> this.delete(d, clazz));

        } else if (clazz.isAssignableFrom(Award.class)) {
            for (Long deleted : entitiesDelete) {
                Entitiable entitiable = find(clazz, deleted);
                entitiable.setEntityCode(null);
                entitiable.setEntityId(null);
            }
        }

    }

    public void remove(List<? extends ReachableEntity> materials) {
        readOnlyHolder.set(Boolean.FALSE);
        materials.forEach(entityManager::remove);
    }

    public void remove(ReachableEntity material) {
        readOnlyHolder.set(Boolean.FALSE);
        entityManager.remove(material);
    }

    @Secured
    public <E extends ReachableEntity> void delete(Long id, Class<E> clazz) {
        delete(find(clazz, id));
    }

    public <E extends ReachableDTO> void delete(E entity) {
        if (Awardable.class.isAssignableFrom(entity.getClass())) {
            List<Award> awards = findAllEntitiesByCodeAndId(
                    Utils.camel2underscore(entity.getClass().getSimpleName()),
                    entity.getId(),
                    Award.class);
            emptyIfNull(awards).stream()
                    .peek(award -> award.setEntityId(null))
                    .forEach(award -> award.setEntityCode(null));
        }
        readOnlyHolder.set(Boolean.FALSE);
        if (Deletable.class.isAssignableFrom(entity.getClass())) {
            Deletable delEntity = (Deletable) entity;
            delEntity.setIsDeleted(Boolean.TRUE);
            entityManager.merge(entity);
        } else {
            entityManager.remove(entity);
        }
        log.info("Delete entity {}  {}", entity.getClass().getSimpleName(), entity.getId());
    }

    private Class<?> getFieldDescriptionMap(Class<?> clazz, String field) {
        if (IdentityEntity.Fields.id.equals(field)) return Long.class;
        if (containsAny(field, BETWEEN)) {
            field = Constants.between(field);
        }

        Set<EntityType<?>> entities = entityManager.getMetamodel().getEntities();
        Map<String, Map<String, ? extends Class<?>>> clazz2attr2type = entities.stream().collect(
                toMap(EntityType::getName, entityType -> entityType.getAttributes().stream()
                        .collect(toMap(Attribute::getName, Attribute::getJavaType))));

        return clazz2attr2type.get(clazz.getSimpleName()).get(field);
    }

    public <R extends RefEntity> List<R> findAllRefs(Set<Integer> ids, Class<R> clazz) {
        if (CollectionUtils.isEmpty(ids)) return Collections.emptyList();
        return find(clazz, ids);
    }

    public AccessTokenPayloadDto getTokenPayload() { // todo cached
        try {
            return Objects.requireNonNull(getTokenPayloadThr());
        } catch (Exception e) {
            Errors.E701.thr(false);
            return null;
        }
    }

    public AccessTokenPayloadDto getTokenPayloadBySystem() { // todo cached
        if (Objects.isNull(RequestContextHolder.getRequestAttributes())) {
            return null;
        }
        return getTokenPayload();
    }

    public AccessTokenPayloadDto getTokenPayloadThr() throws Exception { // todo cached
        log.info("getTokenPayloadThr");
        HttpServletRequest request = ((ServletRequestAttributes) currentRequestAttributes()).getRequest();
        String token = substringAfter(request.getHeader(AUTHORIZATION), "Bearer ");
        if (StringUtils.isBlank(token)) return null;
        return objectMapper.readValue(new String(Base64.getDecoder()
                .decode(token.split("\\.")[1])), AccessTokenPayloadDto.class);
    }

    public ShareLinkDTO getShareLinkThr() throws Exception {
        return getShareLinkThr(getShareLinkToken(), ShareLinkDTO.class);
    }

    private String getShareLinkToken() {
        HttpServletRequest request = ((ServletRequestAttributes) currentRequestAttributes()).getRequest();
        return substringAfter(request.getHeader(COOKIE), "shareToken=");
    }

    public ShareLinkDTO.CookieDTO getShareLink(String url) {
        try {
            return Objects.requireNonNull(getShareLinkThr(url, ShareLinkDTO.CookieDTO.class));
        } catch (Exception e) {
            Errors.E717.thr(false);
            return null;
        }
    }

    public <T> T getShareLinkThr(String url, Class<T> clazz) throws Exception {
        log.info("getShareLink");
        if (StringUtils.isBlank(url)) return null;
        return objectMapper.readValue(Base64Utils.decodeFromString(url), clazz);
    }

    @SuppressWarnings("unchecked")
    public List<Pair<Teacher, PlaceWork>> searchTeachers(Map<String, Object> map) {
        Object orgId = map.remove("organizationId");//costyl
        String str;
        if (Objects.nonNull(orgId)) {
            str = " placeWork.organization.id = " + orgId + " and ";
        } else {
            str = "";
        }
        Function<String, String> transform = s -> replace(s, "e where",
                "e left join PlaceWork placeWork on placeWork.teacher=e where" + str, 1);
        transform = transform.andThen(s -> replace(s, "select e",
                "select e as teacher, placeWork as placeWork", 1)); // max(p) + group by mb?
        Pair<Query, TypedQuery<Long>> query = createQuery(Teacher.class, map,
                PageRequest.of(0, 100), transform); // надеюсь 100 работ ни у кого не было
        List<Tuple> resultList = query.getLeft().getResultList();
        return resultList.stream()
                .map(o -> Pair.of(o.get("teacher", Teacher.class),
                        o.get("placeWork", PlaceWork.class)))
                .collect(Collectors.toList());
    }

    public <T> List<Object> getUniqueValues(Class<T> clazz, String field, Map<String, Object> map) {
        Function<String, String> transform = s -> replace(s, "select e",
                "select e." + field, 1) + " group by e." + field;
        Pair<Query, TypedQuery<Long>> query = createQuery(clazz, map,
                Pageable.unpaged(), transform);
        return CastUtils.cast(query.getLeft().getResultList());
    }

    @SuppressWarnings("unchecked")
    public Map<Long, String> findTeachersFioByStaffId(Set<Long> param, String field) {
        Function<String, String> transform = s -> replace(s, "e from",
                "e.id as id, e.staffId as staffId, concat(e.surname,' ', e.firstName, ' ', "
                        + "coalesce(e.patronymic,''), '|',e.staffId) as name from", 1);//fixme e.staffId
        Pair<Query, TypedQuery<Long>> query = createQuery(Teacher.class,
                Collections.singletonMap(field, param),
                Pageable.unpaged(), transform);
        List<Tuple> resultList = query.getLeft().getResultList();
        return resultList.stream()
                .collect(Collectors.toMap(
                        o -> o.get(field, Long.class),
                        o -> o.get("name", String.class),
                        (o, o2) -> o)
                );
    }
}
