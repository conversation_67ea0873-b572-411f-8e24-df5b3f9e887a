package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import tech.clink.mesh.porteacher.model.common.TeacherEntity;
import tech.clink.mesh.porteacher.model.ref.AwardCodeRef;
import tech.clink.mesh.porteacher.model.ref.AwardImageRef;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.Utils;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.ZonedDateTime;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class GratitudeStudent extends TeacherEntity {
    @DtoField
    private Long gratitudeId;
    @DtoField
    private String gratitudeCategory;
    @DtoField
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String studentMsh;
    @DtoField
    private String studentLastName;
    private String studentFirstName;
    @DtoField
    private String studentPatronymic;
    @DtoField
    private String studentClass;
    @DtoField
    private String gratitudeText;
    @DtoField
    private String subject;
    @DtoField
    @Column(columnDefinition = "smallserial")
    private Long yearEducation;
    private String awardName;
    private Long awardCode;
    @DtoField
    @Enumerated(EnumType.STRING)
    private PublishStatus status;
    private Long giaMarkValue;
    @CreationTimestamp
    private ZonedDateTime createdDate;

    /**
     * Присваивает название награды.
     *
     * @param awardName название награды
     */
    @JsonProperty("rewardName")
    public void setRewardName(String awardName) {
        this.awardName = awardName;
    }

    /**
     * Присваивает код награды.
     *
     * @param awardCode код награды
     */
    @JsonProperty("rewardCode")
    public void setRewardCode(Long awardCode) {
        this.awardCode = awardCode;
    }

    /**
     * Присваивает название награды.
     *
     * @param awardName название награды
     */
    @JsonProperty("awardName")
    public void setAwardName(String awardName) {
        this.awardName = awardName;
    }

    /**
     * Присваивает код награды.
     *
     * @param awardCode код награды
     */
    @JsonProperty("awardCode")
    public void setAwardCode(Long awardCode) {
        this.awardCode = awardCode;
    }

    /**
     * Возвращает название награды.
     *
     * @return название награды.
     */
    @JsonProperty("awardName")
    public String getAwardName() {
        return awardName;
    }

    /**
     * Возвращает код награды.
     *
     * @return код награды.
     */
    @JsonProperty("awardCode")
    public Long getAwardCode() {
        return awardCode;
    }

    /**
     *
     * @param crudService CRUD-сервис
     * @param origin      старая сущность
     */
    @Override
    public void validateOnUpdate(CrudService crudService, Object origin) {
        Errors.E711.thr(PublishStatus.accepted.equals(getStatus())
                || PublishStatus.canceled.equals(getStatus()));
        GratitudeStudent cast = getClass().cast(origin);
        Errors.E711.thr(PublishStatus.unpublished.equals(cast.getStatus()));
    }

    public enum PublishStatus {
        accepted, canceled, unpublished
    }

    @Override
    public void reachTransient(CrudService crudService) {
        map.put(StringUtils.uncapitalize(AwardImageRef.class.getSimpleName()),
                Utils.safGet(crudService.findNullable(AwardCodeRef.class, awardCode), AwardCodeRef::getAwardImageRef));
        super.reachTransient(crudService);
    }
}
