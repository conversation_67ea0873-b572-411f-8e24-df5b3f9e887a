package tech.clink.mesh.porteacher.model.ref;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tech.clink.mesh.porteacher.model.common.Deletable;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
public class EventTypeRef extends RefEntity implements Deletable {
    @ManyToOne
    private EventImageRef eventImageRef;
    private Boolean isDeleted;
}
