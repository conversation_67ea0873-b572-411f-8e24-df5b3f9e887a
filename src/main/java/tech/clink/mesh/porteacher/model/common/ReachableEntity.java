package tech.clink.mesh.porteacher.model.common;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Sets;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.TypeDef;
import tech.clink.mesh.porteacher.model.ref.RefEntity;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Errors;

import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static tech.clink.mesh.porteacher.model.ref.RefEntity.REF_PREFIX;
import static tech.clink.mesh.porteacher.util.Constants.ID;

@Data
@MappedSuperclass
@FieldNameConstants
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public abstract class ReachableEntity implements ReachableDTO {

    @Transient
    @JsonIgnore
    protected Map<String, Object> map = new HashMap<>();

    /**
     * Сеттер для мапы с новыми значениями.
     *
     * @param propertyKey ключ
     * @param value       значение
     */
    @JsonAnySetter
    public void setMap(String propertyKey, Object value) {
        this.map.put(propertyKey, value);
    }

    /**
     * Геттер для мапы с новыми значениями.
     *
     * @return мапа с новыми значениями
     */
    @JsonAnyGetter
    public Map<String, Object> getMap() {
        return map;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @SneakyThrows
    public ReachableEntity reach(CrudService crudService) {
        Set<String> keys = Sets.newHashSet(map.keySet());

        for (String field : keys) {
            String f = StringUtils.removeEnd(field, ID);
            Object value = crudService.find(f, map.remove(field));

            if (RefEntity.class.isAssignableFrom(value.getClass())) {
                f += REF_PREFIX;
            }
            BeanUtils.setProperty(this, f, value);
        }
        Errors.E711.thr(map.isEmpty());

        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ReachableEntity reachOnUpdate(CrudService crudService) {
        return reach(crudService);
    }
}
