package tech.clink.mesh.porteacher.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import tech.clink.mesh.porteacher.model.Attestation;
import tech.clink.mesh.porteacher.model.Education;
import tech.clink.mesh.porteacher.model.PlaceWork;
import tech.clink.mesh.porteacher.model.common.Documentable;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Utils;

import java.util.List;
import java.util.Map;

import static tech.clink.mesh.porteacher.util.Constants.TEACHER_ID;

/**
 * DTO для выходных данных учителя Teacher
 * {@link tech.clink.mesh.porteacher.model.Teacher}.
 */
@Data
@RequiredArgsConstructor
public class Profile {
    @JsonIgnore
    private Long teacherId;

    private Attestation attestation;

    private PlaceWork placeWork;

    private Education education;

    /**
     * {@inheritDoc}
     */
    public void reachTransient(CrudService crudService) {
        Page<Attestation> attestations = crudService.find(Attestation.class,
                Map.of(TEACHER_ID, teacherId, Attestation.Fields.isDeleted, Boolean.FALSE),
                Utils.getPageableWithDefaultSortIfNeeded(Attestation.class, Pageable.ofSize(1)));
        // Errors.E707.thr(attestations.hasContent(), Attestation.class.getSimpleName());
        if (attestations.hasContent()) { // MESTP-358
            Attestation attestation = attestations.iterator().next();
            attestation.reachTransient(crudService);
            this.attestation = attestation;
        }

        List<PlaceWork> placeWorks = Lists.newArrayList(crudService.find(PlaceWork.class,
                Map.of(TEACHER_ID, teacherId, PlaceWork.Fields.isDeleted, Boolean.FALSE),
                Pageable.unpaged()).getContent());
        if (!placeWorks.isEmpty()) {
            placeWorks.sort(PlaceWork::compareTo);
            PlaceWork placeWork = Iterables.getLast(placeWorks);
            placeWork.reachTransient(crudService);
            this.placeWork = placeWork;
        }

        // fixme mb +
        List<Education> educationByType = Lists.newArrayList(crudService.find(Education.class,
                Map.of(TEACHER_ID, teacherId, Documentable.Fields.isDeleted, Boolean.FALSE),
                Pageable.unpaged()).getContent());
        if (!educationByType.isEmpty()) {
            educationByType.sort(Education::compareTo);
            Education education = educationByType.get(0);
            education.reachTransient(crudService);
            this.education = education;
        }
    }
}
