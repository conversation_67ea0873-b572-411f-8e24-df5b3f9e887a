package tech.clink.mesh.porteacher.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.common.Documentable;
import tech.clink.mesh.porteacher.service.kafka.dto.NsiEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class AcademicTitle extends Documentable implements NsiEntity, Deletable {
    @DtoField
    private Long nsiId;

    @CreationTimestamp
    private LocalDateTime createdDate;

    @UpdateTimestamp
    private LocalDateTime updateDate;

    private String name;
    private LocalDate issueDate;
    private String scientificSpecialty;
    private String issuedBy;
    private String docSeries;
    private String docNumber;
    private String educationLevel;
}
