package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;
import tech.clink.mesh.porteacher.model.common.TeacherEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.time.LocalDateTime;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
public class VisibilitySettings extends TeacherEntity {

    @DtoField
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode setting;

    @UpdateTimestamp
    private LocalDateTime updateDate;

}
