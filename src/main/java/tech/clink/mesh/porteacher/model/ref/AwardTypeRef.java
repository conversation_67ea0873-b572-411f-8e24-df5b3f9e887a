package tech.clink.mesh.porteacher.model.ref;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
public class AwardTypeRef extends RefEntity implements Deletable {
    @DtoField
    private Boolean isDeleted;
}
