package tech.clink.mesh.porteacher.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotNull;

/**
 * Вспомогательный класс для удаления {@link DeletedDTO#isDeleted}  = true
 * и сохранения {@link DeletedDTO#isDeleted} = false
 * документов {@link tech.clink.mesh.porteacher.model.DocumentStorage}
 * и наград {@link tech.clink.mesh.porteacher.model.Award}
 * у {@link tech.clink.mesh.porteacher.model.common.Documentable}
 * и {@link tech.clink.mesh.porteacher.model.common.Awardable} сущностей соответственно.
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class DeletedDTO {
    public static final String DOCUMENT = "documents";
    public static final String AWARDS = "awards";

    @NotNull
    private Long id;
    @NotNull
    private Boolean isDeleted;

}
