package tech.clink.mesh.porteacher.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import tech.clink.mesh.porteacher.model.common.Documentable;
import tech.clink.mesh.porteacher.service.kafka.dto.NsiEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class AcademicDegree extends Documentable implements NsiEntity {

    @DtoField
    private Long nsiId;

    private String name;

    private LocalDate issueDate;

    private String committee;

    private String docSeriesNumber;

    private String issuedBy;

    @CreationTimestamp
    private LocalDateTime createdDate;

    @UpdateTimestamp
    private LocalDateTime updateDate;

    private String educationLevel;
}
