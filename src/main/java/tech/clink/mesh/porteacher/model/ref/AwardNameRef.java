package tech.clink.mesh.porteacher.model.ref;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class AwardNameRef extends RefEntity implements Deletable {

    @DtoField
    @ManyToOne
    private AwardTypeRef awardTypeRef;

    @ManyToOne
    private AwardImageRef awardImageRef;

    @DtoField
    private Boolean isDeleted;

}
