package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.time.LocalDate;

import static org.apache.commons.lang3.StringUtils.SPACE;
import static tech.clink.mesh.porteacher.util.Constants.DOT;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class Teacher extends IdentityEntity implements Deletable {
    public static final String TEACHER = "teacher";

    @DtoField
    private Long staffId;

    @DtoField
    private String firstName;

    @DtoField
    private String surname;

    private String patronymic;

    @DtoField
    @Column(length = 15)
    private String snils;

    @Column(length = 36)
    private String personnelNum;

    @DtoField
    private LocalDate birthDate;

    private String phoneNumber;

    private String mail;

    private String address;

    @DtoField
    private Boolean isDeleted;

    /**
     * Метод для получения ФИО учителя в заданном формате.
     *
     * @return Фамилия И.О. учителя или Фамилия Имя, если отсутствует отчество
     */
    @JsonIgnore
    public String getFio() {
        return StringUtils.isBlank(patronymic) ? surname + SPACE + firstName
                : surname + SPACE + firstName.charAt(0) + DOT + patronymic.charAt(0) + DOT;
    }

}
