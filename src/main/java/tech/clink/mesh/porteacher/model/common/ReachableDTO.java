package tech.clink.mesh.porteacher.model.common;

import tech.clink.mesh.porteacher.model.repo.GenericIdProjection;
import tech.clink.mesh.porteacher.service.CrudService;

import java.util.Map;

/**
 * Интерфейс для DTO-сущностей.
 */
public interface ReachableDTO extends GenericIdProjection<Long> {
    /**
     * Метод для преобразования DTO в Entity.
     *
     * @param crudService CRUD-сервис
     * @return Сущность с id
     */
    ReachableDTO reach(CrudService crudService);

    /**
     * Метод для преобразования DTO с новыми занчениями при обновлении в Entity.
     *
     * @param crudService CRUD-сервис
     * @return Сущность с новыми значениями
     */
    ReachableDTO reachOnUpdate(CrudService crudService);

    /**
     * Метод для валидации DTO при создании.
     *
     * @param crudService CRUD-сервис
     */
    default void validateOnCreate(CrudService crudService) {
    }

    /**
     * Метод для валидации DTO при обновлении.
     *
     * @param crudService CRUD-сервис
     */
    default void validateOnUpdate(CrudService crudService) {
    }

    /**
     * Метод для валидации DTO при обновлении.
     *
     * @param crudService CRUD-сервис
     * @param origin      старая сущность
     */
    default void validateOnUpdate(CrudService crudService, Object origin) {
    }

    /**
     * Метод для преобразования Entity в DTO, для get-методов.
     *
     * @param crudService CRUD-сервис
     */
    default void reachTransient(CrudService crudService) {
    }

    /**
     * Метод получения дополнительных данных.
     *
     * @return мапа с дополнительными данными
     */
    Map<String, Object> getMap();
}