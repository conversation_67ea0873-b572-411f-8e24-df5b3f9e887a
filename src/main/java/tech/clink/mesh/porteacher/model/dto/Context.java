package tech.clink.mesh.porteacher.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import tech.clink.mesh.porteacher.model.Teacher;

import static tech.clink.mesh.porteacher.model.Teacher.Fields.address;
import static tech.clink.mesh.porteacher.model.Teacher.Fields.isDeleted;
import static tech.clink.mesh.porteacher.model.Teacher.Fields.personnelNum;
import static tech.clink.mesh.porteacher.model.Teacher.Fields.snils;
import static tech.clink.mesh.porteacher.model.Teacher.Fields.staffId;

/**
 * DTO для выходных данных учителя Teacher
 * {@link tech.clink.mesh.porteacher.model.Teacher}.
 */
@Data
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({address, isDeleted, snils, staffId, personnelNum})
public class Context extends Teacher {

    @JsonProperty("phone")
    private String phoneNumber;
}