package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.common.Documentable;
import tech.clink.mesh.porteacher.model.common.Entitiable;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.ref.AwardCodeRef;
import tech.clink.mesh.porteacher.model.ref.AwardNameRef;
import tech.clink.mesh.porteacher.model.ref.AwardTypeRef;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.Utils;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Сущность наград учителя.
 */
@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class Award extends Documentable implements Deletable, Entitiable {

    @DtoField
    @ManyToOne
    private AwardTypeRef awardTypeRef;

    @DtoField
    @ManyToOne
    @JsonIgnoreProperties(AwardCodeRef.Fields.awardTypeRef)
    private AwardCodeRef awardCodeRef;

    @ManyToOne
    private AwardNameRef awardNameRef;

    private String customAwardName;

    @DtoField
    private LocalDate assignmentDate;

    @DtoField
    private String documentDescription;

    private String description;

    private String eventLink;

    @CreationTimestamp
    private LocalDateTime createdDate;

    @UpdateTimestamp
    private LocalDateTime updateDate;

    private String entityCode;

    private Long entityId;

    /**
     * {@inheritDoc}
     */
    @SneakyThrows
    @Override
    public void validateOnCreate(CrudService crudService) {
        Errors.E711.thr(Objects.nonNull(map.get("awardNameId")) ^ StringUtils.isNotBlank(customAwardName));
    }

    /**
     * Метод для частичного обновления сущности награды.
     *
     * @param crudService CRUD-сервис
     * @param newAward    измененные данные о награде
     * @return награда с обновленными данными
     */
    @SneakyThrows
    public IdentityEntity forUpdate(Award newAward, CrudService crudService) {
        newAward.validateOnCreate(crudService);

        Utils.copyNonNullProperties(newAward, this);
        if (Objects.nonNull(newAward.getDocs())) {
            this.setDocuments(newAward.getDocs());
        }

        return reach(crudService); // fixme mb unnecesary validate
    }
}
