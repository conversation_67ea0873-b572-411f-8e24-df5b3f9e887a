package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.common.ReachableEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Organization extends ReachableEntity implements Deletable {

    @Id
    @Column(unique = true, nullable = false)
    private Long id;

    @DtoField
    private String shortName;

    @DtoField
    private String fullName;

    private String address;

    @DtoField
    @JsonIgnore
    private Boolean isDeleted;

}

