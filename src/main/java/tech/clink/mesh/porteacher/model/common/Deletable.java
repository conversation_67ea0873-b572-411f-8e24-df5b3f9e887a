package tech.clink.mesh.porteacher.model.common;

/**
 * Интерфейс для мягкого удаления записей.
 */
public interface Deletable extends ReachableDTO {
    String FIELD = "isDeleted";

    /**
     * Устанавливает записи значение isDeleted.
     *
     * @param isDeleted true если запись удалена, false если не удалена
     */
    void setIsDeleted(Boolean isDeleted);

    /**
     * Получает у записи значение isDeleted.
     *
     * @return true если запись удалена, false если не удалена
     */
    Boolean getIsDeleted();
}
