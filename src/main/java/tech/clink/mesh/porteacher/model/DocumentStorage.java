package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.UpdateTimestamp;
import tech.clink.mesh.porteacher.model.common.Entitiable;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Transient;
import java.time.LocalDateTime;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class DocumentStorage extends IdentityEntity implements Entitiable {

    @Column(length = 50) //@Max(50) -> exception on "project"
    private String entityCode;

    private Long entityId;

    @DtoField
    private String link;

    @DtoField
    @Column(length = 50)
    private String uuid;

    @DtoField
    private String name;

    @DtoField
    private Long size;

    @DtoField
    private String format;

    @DtoField
    private Long createdBy;

    @UpdateTimestamp
    private LocalDateTime updateDate;

    @DtoField
    private Boolean verification;

    @Transient
    @JsonIgnore
    private byte[] body;
}
