package tech.clink.mesh.porteacher.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import tech.clink.mesh.porteacher.model.common.DeletableTeacherEntity;
import tech.clink.mesh.porteacher.model.ref.InterestCodeRef;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class ProfessionalInterest extends DeletableTeacherEntity {

    @DtoField
    @ManyToOne
    private InterestCodeRef interestCodeRef;

}
