package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import tech.clink.mesh.porteacher.model.common.Entitiable;
import tech.clink.mesh.porteacher.model.common.TeacherEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.time.LocalDateTime;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
public class ChangeHistory extends TeacherEntity implements Entitiable {

    @DtoField
    private Long userId;

    @DtoField
    private Integer roleId;

    private String entityCode;

    private String entityName;

    private Long entityId;

    @DtoField
    @Column(length = 50)
    private String method;

    @CreationTimestamp
    private LocalDateTime createdDate;

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode oldObject;

    @DtoField
    private Boolean isActual;

}
