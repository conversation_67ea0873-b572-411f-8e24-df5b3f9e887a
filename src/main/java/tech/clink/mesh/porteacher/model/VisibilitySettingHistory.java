package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.annotations.CreationTimestamp;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.ref.SectionRef;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Constants;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class VisibilitySettingHistory extends IdentityEntity {

    @CreationTimestamp
    private LocalDateTime createdDate;

    @DtoField
    @JsonIgnore
    private Long createdBy;

    @DtoField
    private Long sectionId;

    @DtoField
    private Boolean isVisible;

    private String reason;

    @DtoField
    private Long parentId;

    /**
     * Генератор истории изменения настроек видимости.
     *
     * @param difference разница настроек.
     * @param reason     причина изменения.
     * @param id         ид учителя.
     * @return список истории.
     */
    public static List<VisibilitySettingHistory> build(Map<Long, Pair<Boolean, Long>> difference,
                                                       String reason, Long id) {
        List<VisibilitySettingHistory> result = new LinkedList<>();
        for (Long key : difference.keySet()) {
            VisibilitySettingHistory e = new VisibilitySettingHistory();
            e.setReason(reason);
            e.setCreatedBy(id);
            e.setSectionId(key);
            e.setIsVisible(difference.get(key).getLeft());
            e.setParentId(difference.get(key).getRight());
            result.add(e);
        }
        return result;
    }

    @Override
    public void reachTransient(CrudService crudService) {
        super.getMap().put(Fields.createdBy, NumberUtils.LONG_ZERO.equals(createdBy)
                ? Constants.SYSTEM : crudService.find(Teacher.class, createdBy).getFio());
        super.getMap().put("sectionName", crudService.find(SectionRef.class, sectionId).getName());

        super.reachTransient(crudService);
    }
}
