package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.ref.ActionCodeRef;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.time.LocalDateTime;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({IdentityEntity.Fields.id})
public class LogAction extends IdentityEntity {
    @DtoField
    @ManyToOne
    @JsonIgnore
    private ActionCodeRef actionCodeRef;

    @UpdateTimestamp
    private LocalDateTime actionDate;

    @DtoField
    private Long staffId;

    @DtoField
    private String route;

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode requestBody;

    @DtoField
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode responseBody;

    @DtoField
    private Boolean isSuccessful;

    @Override
    public void reachTransient(CrudService crudService) {
        super.reachTransient(crudService);
        map.put("actionId", super.getId());
        map.put("actionCodeId", actionCodeRef.getId());
    }
}
