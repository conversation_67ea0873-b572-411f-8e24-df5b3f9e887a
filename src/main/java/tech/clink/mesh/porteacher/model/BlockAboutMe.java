package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import tech.clink.mesh.porteacher.model.dto.AboutAndCredo;

import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@FieldNameConstants
@Table(name = "about")
@EqualsAndHashCode(callSuper = true)
public class BlockAboutMe extends About {
    @Embedded
    @JsonUnwrapped
    private AboutAndCredo aboutMe;

    @Override
    @JsonIgnore
    public AboutAndCredo getInfo() {
        return aboutMe;
    }
}
