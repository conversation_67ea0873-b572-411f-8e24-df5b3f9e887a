package tech.clink.mesh.porteacher.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.common.Documentable;
import tech.clink.mesh.porteacher.model.ref.CategoryEducationRef;
import tech.clink.mesh.porteacher.model.ref.OrganizationCodeRef;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@Table(name = "education_additional")
public class AdditionalEducation extends Documentable implements Deletable {

    private Long dpoId;

    private String organizationName;

    @DtoField
    private String courseName;

    @DtoField
    private LocalDate startDate;

    @DtoField
    private LocalDate endDate;

    @DtoField
    private Integer courseDuration;

    @DtoField
    private String courseRealisation;

    @DtoField
    private String courseFormat;

    private String annotation;

    @ManyToOne
    private CategoryEducationRef categoryEducationRef;

    @ManyToOne
    private OrganizationCodeRef organizationCodeRef;

    @CreationTimestamp
    private LocalDateTime createdDate;

    @UpdateTimestamp
    private LocalDateTime updateDate;

    /**
     * {@inheritDoc}
     */
    @Override
    public void validateOnCreate(CrudService crudService) {
        Errors.E711.thr(StringUtils.isBlank(organizationName) ^ Objects.isNull(map.get("organizationCodeId")));
    }
}
