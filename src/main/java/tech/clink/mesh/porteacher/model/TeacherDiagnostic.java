package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.common.TeacherEntity;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class TeacherDiagnostic extends TeacherEntity implements Deletable {
    @DtoField
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long diagnosticId;

    @DtoField
    @JsonAlias("passedAt")
    private LocalDate diagnosticDate;

    @DtoField
    private String subject;

    @DtoField
    @JsonAlias("kind")
    private String type;

    @DtoField
    @Column(columnDefinition = "int2")
    private Integer primaryScore;

    @DtoField
    @JsonAlias("diagnosticResult")
    @Column(columnDefinition = "int2")
    private Integer resultPercent;

    @DtoField
    private String diagnosticLevel;

    @DtoField
    @JsonAlias("publishedSign")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Boolean isPublished;

    @DtoField
    @JsonAlias("publishedAt")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private LocalDate publishDate;

    @DtoField
    @CreatedDate
    private LocalDateTime createdDate;

    @DtoField
    @JsonIgnore
    @LastModifiedDate
    private LocalDateTime updatedDate;

    @DtoField
    @JsonIgnore
    private Boolean isDeleted;

    @Override
    public TeacherDiagnostic reach(CrudService crudService) {
        if (Objects.isNull(getTeacher())) {
            Object snils = map.remove(Teacher.Fields.snils);
            Teacher teacher = crudService.find(Teacher.class, String.class, Teacher.Fields.snils, snils);
            Errors.E707.thr(Objects.nonNull(teacher), Teacher.class.getSimpleName());
            setTeacher(teacher);
        }

        super.reach(crudService);

        return this;
    }

    @Override
    public void reachTransient(CrudService crudService) {

    }
}
