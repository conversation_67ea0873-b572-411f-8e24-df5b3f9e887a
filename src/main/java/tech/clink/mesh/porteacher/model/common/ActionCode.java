package tech.clink.mesh.porteacher.model.common;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Перечисление возможных действий для логов.
 */
@Getter
@RequiredArgsConstructor
public enum ActionCode {
    UPLOADING_DATA_TO_PORTFOLIO(1L, "Загрузка данных в свое портфолио"),
    EDITING_DATA_OF_PORTFOLIO(2L, "Редактирование доступных данных своего портфолио, ранее загруженных учителем"),
    ADDING_FILE_TO_DATA_LOADED_AUTOMATICALLY(3L, "Добавление файла к данным, загруженным автоматически"),
    LINKING_DATA_TO_PORTFOLIO(4L, "Связывание данных в портфолио"),
    DELETING_DATA_OF_PORTFOLIO(5L, "Удаление доступных данных своего портфолио"),
    GENERATING_LINK(6L, "Генерация ссылки, по которой публикуются данные портфолио"),
    DOWNLOADING_QR_CODE(7L, "Генерации QR-кода для ссылки, по которой публикуются данные портфолио"),
    DOWNLOADING_PDF(8L, "Выгрузка портфолио в файл"),
    DOWNLOADING_FILE(9L, "Скачивание файла, приложенного к данным"),
    GETTING_INFORMATION(10L, "Получение информации"),
    UPLOADING_FILE(11L, "Загрузка файла"),
    OTHER(12L, "Прочее"),
    UNAUTHORIZED_USER_ACTION(13L, "Действия неавторизованного пользователя"),
    NONE(null, StringUtils.EMPTY);

    public static final Map<Long, ActionCode> ACTION_CODES = Arrays.stream(values())
            .collect(Collectors.toMap(ActionCode::getValue, Function.identity()));

    private final Long value;
    private final String name;

    /**
     * Метод для преобразования Long в Enum.
     *
     * @param value код
     * @return экземпляр ActionCode
     */
    public static ActionCode getByValue(Long value) {
        return ACTION_CODES.get(value);
    }
}
