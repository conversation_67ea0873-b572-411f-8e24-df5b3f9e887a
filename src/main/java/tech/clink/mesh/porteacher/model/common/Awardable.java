package tech.clink.mesh.porteacher.model.common;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import tech.clink.mesh.porteacher.model.Award;
import tech.clink.mesh.porteacher.model.dto.DeletedDTO;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Utils;

import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.util.List;

/**
 * Абстрактный класс для записей, которые могут иметь награды и документы.
 * Сущностей, которые могут иметь только награды, в данный момент нет
 */
@Data
@MappedSuperclass
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public abstract class Awardable extends Documentable {
    public static final String AWARDS = "awards";

    @Transient
    @JsonIgnore
    private List<Award> awards;

    @Transient
    @JsonIgnore
    private List<DeletedDTO> aws;

    /**
     * Сеттер для проекций наград.
     *
     * @param awards проекции наград
     */
    public void setAwards(List<DeletedDTO> awards) {
        this.aws = awards;
    }

    /**
     * Геттер для наград.
     *
     * @return награды
     */
    @JsonGetter(AWARDS)
    public List<Award> getAwards() {
        return awards;
    }

    /**
     * Заглушка для получения наград.
     *
     * @return проекция наград
     */
    @JsonIgnore
    public List<DeletedDTO> getAws() {
        return aws;
    }

    /**
     * Сеттер для наград.
     *
     * @param awards награды
     */
    public void setAws(List<Award> awards) {
        this.awards = awards;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void reachTransient(CrudService crudService) {
        String code = Utils.camel2underscore(getClass().getSimpleName());
        setAws(crudService.findNotDeletedEntitiesByCodeAndId(code, getId(), Award.class));
        getAwards().forEach(award -> award.reachTransient(crudService));

        super.reachTransient(crudService);
    }
}
