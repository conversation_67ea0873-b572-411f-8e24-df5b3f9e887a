package tech.clink.mesh.porteacher.model.common;

/**
 * Интерфейс для сущностей, которые
 * могут иметь entityCode и entityId.
 *
 */
public interface Entitiable {

    /**
     * Устанавливает записи значение entityCode.
     * Обычно {@link org.apache.commons.lang3.StringUtils#uncapitalize}(getClass().simpleName())}
     *
     * @param entityCode код сущности
     */
    void setEntityCode(String entityCode);

    /**
     * Получает у записи значение entityCode.
     *
     * @return Код сущности
     */
    String getEntityCode();

    /**
     * Устанавливает записи значение entityId, соответствующее id сущности с
     * entityCode = {@link org.apache.commons.lang3.StringUtils#uncapitalize}(getClass().simpleName())}.
     *
     * @param entityId ID сущности
     */
    void setEntityId(Long entityId);

    /**
     * Получает у записи значение entityId.
     *
     * @return ID сущности
     */
    Long getEntityId();
}
