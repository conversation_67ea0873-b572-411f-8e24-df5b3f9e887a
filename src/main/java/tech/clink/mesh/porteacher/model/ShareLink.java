package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import tech.clink.mesh.porteacher.model.common.TeacherEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Optional;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class ShareLink extends TeacherEntity {

    @DtoField
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode shareSettings;

    @DtoField
    private ZonedDateTime accessStartDate;

    @DtoField
    private ZonedDateTime accessEndDate;

    @DtoField
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String url;

    @DtoField
    private Boolean isActive;

    @CreationTimestamp
    private LocalDateTime createdDate;

    /**
     * Добавляет qr code для перехода по ссылке.
     *
     * @param qrCode qr код
     */
    public void reachTransientQrCode(String qrCode) {
        map.put("qrCode", qrCode);
    }

    /**
     * Добавляет абсолютный путь для перехода по ссылки.
     *
     * @param serverUrl доменный путь
     */
    public void reachTransientUrl(String serverUrl) {
        Optional.ofNullable(serverUrl).ifPresentOrElse(
                url -> map.put(Fields.url, getFullUrl(url)),
                () -> map.put(Fields.url, url));
    }

    /**
     * Добавляет абсолютный путь для перехода по ссылки.
     *
     * @param serverUrl доменный путь
     * @return абсолютный путь
     */
    public String getFullUrl(String serverUrl) {
        return serverUrl + "/share-links/" + url;
    }
}
