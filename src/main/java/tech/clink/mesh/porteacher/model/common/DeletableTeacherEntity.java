package tech.clink.mesh.porteacher.model.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

/**
 * Класс для сущностей, имеющих поле {@link DeletableTeacherEntity#isDeleted},
 * не участвующее в "мягком" удалении. Такими классами являются
 * {@link tech.clink.mesh.porteacher.model.ReferenceCard} и
 * {@link tech.clink.mesh.porteacher.model.Subject}
 */

@Data
@MappedSuperclass
@EqualsAndHashCode(callSuper = true)
public abstract class DeletableTeacherEntity extends TeacherEntity {

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Boolean isDeleted;

}
