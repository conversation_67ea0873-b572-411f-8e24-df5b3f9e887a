package tech.clink.mesh.porteacher.model.dto;

import lombok.Data;
import tech.clink.mesh.porteacher.model.ShareLink;
import tech.clink.mesh.porteacher.util.validation.DtoField;

@Data
public class ShareLinkDTO {
    @DtoField
    private Long teacherId;

    @DtoField
    private Boolean isActive;

    @Data
    public static class CookieDTO {
        private Long id;
        private String url;
        private Long teacherId;

        /**
         * Собирает дтошку для куки.
         *
         * @param shareLink моделька
         * @return дто
         */
        public static CookieDTO build(ShareLink shareLink) {
            CookieDTO cookieDTO = new CookieDTO();
            cookieDTO.setId(shareLink.getId());
            cookieDTO.setUrl(shareLink.getUrl());
            cookieDTO.setTeacherId(shareLink.getTeacher().getId());

            return cookieDTO;
        }
    }
}
