package tech.clink.mesh.porteacher.model.ref;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import tech.clink.mesh.porteacher.model.common.Deletable;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class EventCodeRef extends RefEntity implements Deletable {
    @ManyToOne
    private EventTypeRef eventTypeRef;
    private Boolean isDeleted;
}

