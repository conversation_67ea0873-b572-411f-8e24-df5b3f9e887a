package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.time.LocalDateTime;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class AdministratorSettings extends IdentityEntity {

    @DtoField
    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode settings;

    @CreationTimestamp
    private LocalDateTime createdDate;

}
