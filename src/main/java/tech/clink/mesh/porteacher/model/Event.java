package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import tech.clink.mesh.porteacher.model.common.Awardable;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.ref.EventCodeRef;
import tech.clink.mesh.porteacher.model.ref.PresenceFormatRef;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class Event extends Awardable implements Deletable {
    @JsonIgnore
    private Long dpoId;

    @DtoField
    @ManyToOne
    private EventCodeRef eventCodeRef;

    private String stage;

    private String name;

    private LocalDate stageDate;

    private String annotation;

    @DtoField
    private String format;

    private String link;

    @DtoField
    private LocalDate date;

    @UpdateTimestamp
    private LocalDateTime updateDate;

    @CreationTimestamp
    private LocalDateTime createdDate;

    @DtoField
    @ManyToOne
    private PresenceFormatRef presenceFormatRef;

    private String projectLink;

    private String result;

    @OneToMany(mappedBy = RoleAtEvent.Fields.eventId)
    private List<RoleAtEvent> roles;

    @Override
    public void reachTransient(CrudService crudService) {
        setRoles(crudService.findAll(RoleAtEvent.class, RoleAtEvent.Fields.eventId, getId()));
        getRoles().forEach(role -> role.reachTransient(crudService));
        super.reachTransient(crudService);
    }
}
