package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.springframework.util.comparator.Comparators;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.common.TeacherEntity;
import tech.clink.mesh.porteacher.model.dto.aupd.AccessTokenPayloadDto;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.service.kafka.dto.NsiEntity;
import tech.clink.mesh.porteacher.util.Constants;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.Optional;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class PlaceWork extends TeacherEntity implements Deletable, NsiEntity, Comparable<PlaceWork> {

    @DtoField
    @JsonIgnore
    private Long nsiId;

    @ManyToOne //(cascade = CascadeType.ALL)
    private Organization organization;

    private LocalDate startDate;

    private LocalDate endDate;

    private String position;

    private String employmentKind;

    private Boolean isPedagogicalStaff;

    @DtoField
    private Boolean verification;

    @DtoField
    @JsonIgnore
    private Boolean isDeleted;

    //    @JsonIgnore
    @CreationTimestamp
    private LocalDateTime createdDate;

    @DtoField
    @JsonIgnore
    private Long createdBy;

    @Override
    public int compareTo(PlaceWork o) {
        int i = Comparator.comparing(PlaceWork::getEndDate, Comparators.nullsHigh()).compare(this, o);
        if (i == 0) {
            i = Constants.MAIN_PLACE_OF_WORK.equalsIgnoreCase(employmentKind) ? 1 : -1;
        }
        return i;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public IdentityEntity reach(CrudService crudService) {
        super.reach(crudService);

        AccessTokenPayloadDto tokenPayload = crudService.getTokenPayloadBySystem();
        setCreatedBy(ObjectUtils.defaultIfNull(createdBy,
                Optional.ofNullable(tokenPayload).map(AccessTokenPayloadDto::getStf).orElse(NumberUtils.LONG_ZERO)));

        return this;
    }
}
