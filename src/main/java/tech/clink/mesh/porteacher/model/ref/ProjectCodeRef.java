package tech.clink.mesh.porteacher.model.ref;


import lombok.Data;
import lombok.EqualsAndHashCode;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;

@Data
@Entity
@EqualsAndHashCode(callSuper = true)
public class ProjectCodeRef extends RefEntity implements Deletable {
    @DtoField
    @ManyToOne
    private ProjectTypeRef projectTypeRef;
    private Boolean isDeleted;
}
