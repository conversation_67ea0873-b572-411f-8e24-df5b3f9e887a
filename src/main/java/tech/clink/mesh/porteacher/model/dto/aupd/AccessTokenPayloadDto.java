package tech.clink.mesh.porteacher.model.dto.aupd;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccessTokenPayloadDto {
    private Long sub;
    private String aud;
    private String rls;
    private String nbf;
    private Long stf;
    private String msh;
    private String iss;
    private String exp;
    private Long iat;
    private UUID jti;
    private String sso;
}
