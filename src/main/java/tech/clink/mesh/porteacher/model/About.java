package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import tech.clink.mesh.porteacher.model.common.ReachableEntity;
import tech.clink.mesh.porteacher.model.common.TeacherEntity;
import tech.clink.mesh.porteacher.model.dto.AboutAndCredo;
import tech.clink.mesh.porteacher.service.CrudService;

import javax.persistence.Transient;
import java.util.Collections;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public abstract class About extends TeacherEntity {
    @Transient
    @JsonIgnore
    protected Map<String, Object> map = Collections.emptyMap();

    /**
     * {@inheritDoc}
     */
    @Override
    @SneakyThrows
    public ReachableEntity reach(CrudService crudService) {
        return this;
    }

    /**
     * Возвращает универсальную информацию.
     *
     * @return информацию об учителе.
     */
    @JsonIgnore
    public abstract AboutAndCredo getInfo();
}
