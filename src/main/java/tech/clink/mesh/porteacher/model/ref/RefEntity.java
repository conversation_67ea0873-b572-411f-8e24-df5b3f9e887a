package tech.clink.mesh.porteacher.model.ref;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.MappedSuperclass;

@Data
@MappedSuperclass
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public abstract class RefEntity extends IdentityEntity {
    public static final String REF_PREFIX = "Ref";

    @DtoField
    private String name;

}
