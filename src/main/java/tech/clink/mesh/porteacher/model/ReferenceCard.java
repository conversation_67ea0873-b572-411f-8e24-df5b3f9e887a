package tech.clink.mesh.porteacher.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import tech.clink.mesh.porteacher.model.common.DeletableTeacherEntity;
import tech.clink.mesh.porteacher.model.ref.MediaSocialRef;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.util.Objects;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class ReferenceCard extends DeletableTeacherEntity {

    @DtoField
    @ManyToOne //(cascade = CascadeType.ALL)
    private MediaSocialRef mediaSocialRef;

    @DtoField
    private String link;

    @Override
    public void validateOnCreate(CrudService crudService) {
        Errors.E708.thr(Objects.nonNull(getIsDeleted()), "isDeleted");
    }

}
