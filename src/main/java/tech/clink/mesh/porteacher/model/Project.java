package tech.clink.mesh.porteacher.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import tech.clink.mesh.porteacher.model.common.Awardable;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.ref.ProjectCodeRef;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class Project extends Awardable implements Deletable {
    @DtoField
    @ManyToOne
    private ProjectCodeRef projectCodeRef;

    private String name;

    @DtoField
    private LocalDate startDate;

    @DtoField
    private LocalDate endDate;

    private String link;

    private String description;

    @UpdateTimestamp
    private LocalDateTime updateDate;

    @CreationTimestamp
    private LocalDateTime createdDate;
}
