package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.common.Documentable;
import tech.clink.mesh.porteacher.service.kafka.dto.NsiEntity;
import tech.clink.mesh.porteacher.util.Constants;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class Education extends Documentable implements Deletable, NsiEntity, Comparable<Education> {
    @DtoField
    @JsonIgnore
    private Long nsiId;

    @CreationTimestamp
    private LocalDateTime createdDate;

    private String institution;
    private String type;
    private String educationForm;
    private LocalDate startDate;
    private LocalDate endDate;
    private String specialtyName;
    private String qualification;
    private Boolean isExcellent;
    private String docType;
    private String docSeries;
    private String docNumber;
    private LocalDate docIssueDate;

    @Override
    public int compareTo(Education o) {
        int i = Arrays.stream(Type.values())
                .filter(type -> type.getValue().equalsIgnoreCase(o.getType()))
                .findFirst()
                .orElse(Type.DEFAULT)
                .compareTo(Arrays.stream(Type.values())
                        .filter(type -> type.getValue().equalsIgnoreCase(this.getType()))
                        .findFirst()
                        .orElse(Type.DEFAULT));
        if (i == 0) {
            i = o.getDocIssueDate().compareTo(this.getDocIssueDate());
        }
        return i;
    }

    @Getter
    private enum Type {
        DEFAULT(StringUtils.EMPTY),
        SECONDARY(Constants.SECONDARY),
        BACHELOR(Constants.BACHELOR),
        MASTER(Constants.MASTER),
        HIGHLY_QUALIFIED(Constants.HIGHLY_QUALIFIED);

        private final String value;

        Type(String value) {
            this.value = value;
        }
    }
}
