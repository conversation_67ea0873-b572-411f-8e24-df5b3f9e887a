package tech.clink.mesh.porteacher.model.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import tech.clink.mesh.porteacher.model.Teacher;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

import static tech.clink.mesh.porteacher.util.Constants.TEACHER__ID;

@Data
@MappedSuperclass
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public abstract class TeacherEntity extends IdentityEntity {

    @DtoField
    @ManyToOne//(cascade = CascadeType.ALL)
    @JsonIgnore
    private Teacher teacher;

    @Override
    public void reachTransient(CrudService crudService) {
        super.map.put(TEACHER__ID, getTeacher().getId());
    }
}
