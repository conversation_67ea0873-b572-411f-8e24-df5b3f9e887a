package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.ObjectUtils;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.ref.RoleTypeRef;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class RoleAtEvent extends IdentityEntity {

    @DtoField
    @JsonIgnore
    private Long eventId; //todo entity?

    @DtoField
    @ManyToOne
    private RoleTypeRef roleTypeRef;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Boolean isDeleted;

    @Override
    public void reachTransient(CrudService crudService) {
        setIsDeleted(ObjectUtils.defaultIfNull(isDeleted, Boolean.FALSE));
        super.reachTransient(crudService);
    }
}
