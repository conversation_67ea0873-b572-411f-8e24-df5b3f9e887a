package tech.clink.mesh.porteacher.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Embeddable;

/**
 * Вспомогательный класс для входных данных About.
 */

@Data
@Embeddable
@FieldNameConstants
public class AboutAndCredo {

    private String info;

    private String quote;

    private String quoteAuthor;

    /**
     * Проверяет на наличие информации.
     *
     * @return признак наличия данных.
     */
    @JsonIgnore
    public Boolean isEmpty() {
        return StringUtils.isAllEmpty(info, quote, quoteAuthor);
    }
}
