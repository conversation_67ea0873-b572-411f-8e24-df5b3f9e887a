package tech.clink.mesh.porteacher.model.common;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import tech.clink.mesh.porteacher.model.DocumentStorage;
import tech.clink.mesh.porteacher.model.Teacher;
import tech.clink.mesh.porteacher.model.dto.DeletedDTO;
import tech.clink.mesh.porteacher.model.dto.aupd.AccessTokenPayloadDto;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Constants;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.Utils;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.util.List;
import java.util.Optional;

/**
 * Интерфейс для записей, которые могут иметь документы.
 */
@Data
@MappedSuperclass
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public abstract class Documentable extends TeacherEntity implements Deletable {
    public static final String DOCS = "documents";

    @Transient
    @JsonIgnore
    private List<DocumentStorage> documents;

    @Transient
    @JsonIgnore
    private List<DeletedDTO> docs;

    @DtoField
    private Boolean isDeleted;

    @DtoField
    @JsonIgnore
    private Long createdBy;

    @DtoField
    private Boolean verification;

    /**
     * Сеттер для проекций документов.
     *
     * @param documents проекций документов
     */
    public void setDocuments(List<DeletedDTO> documents) {
        this.docs = documents;
    }

    /**
     * Геттер документов.
     *
     * @return документы
     */
    @JsonGetter(DOCS)
    public List<DocumentStorage> getDocuments() {
        return documents;
    }

    /**
     * Возвращает проекции документов.
     *
     * @return проекции документов
     */
    @JsonIgnore
    public List<DeletedDTO> getDocs() {
        return docs;
    }

    /**
     * Сеттер для документов.
     *
     * @param documents документов
     */
    public void setDocs(List<DocumentStorage> documents) {
        this.documents = documents;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IdentityEntity reach(CrudService crudService) {
        super.reach(crudService);

        setIsDeleted(ObjectUtils.defaultIfNull(getIsDeleted(), Boolean.FALSE));

        AccessTokenPayloadDto tokenPayload = crudService.getTokenPayloadBySystem();
        setCreatedBy(ObjectUtils.defaultIfNull(createdBy,
                Optional.ofNullable(tokenPayload).map(AccessTokenPayloadDto::getStf).orElse(NumberUtils.LONG_ZERO)));
        setVerification(ObjectUtils.defaultIfNull(verification, !StringUtils.equalsAny(
                Optional.ofNullable(tokenPayload).map(AccessTokenPayloadDto::getAud).orElse(null),
                crudService.getAudTeacher())));

        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ReachableEntity reachOnUpdate(CrudService crudService) {
        return super.reach(crudService);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void reachTransient(CrudService crudService) {
        Errors.E713.thr(!isDeleted, getClass().getSimpleName(), getId());

        String code = Utils.camel2underscore(getClass().getSimpleName());
        setDocs(crudService.findAllEntitiesByCodeAndId(code, getId(), DocumentStorage.class));

        super.getMap().put(Fields.createdBy, NumberUtils.LONG_ZERO.equals(createdBy) ? Constants.SYSTEM
                : Utils.safGet(crudService.findTeacherByStaffId(createdBy), Teacher::getFio));

        super.reachTransient(crudService);
    }
}
