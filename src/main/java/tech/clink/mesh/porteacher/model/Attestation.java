package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.annotations.CreationTimestamp;
import tech.clink.mesh.porteacher.model.common.Deletable;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.common.TeacherEntity;
import tech.clink.mesh.porteacher.model.dto.aupd.AccessTokenPayloadDto;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.service.kafka.dto.NsiEntity;
import tech.clink.mesh.porteacher.util.Constants;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.Entity;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class Attestation extends TeacherEntity implements Deletable, NsiEntity {

    @DtoField
    @JsonIgnore
    private Long nsiId;

    private String qualificationCategory;

    private String issuedBy;

    private LocalDate attestationDate;

    private String cause;

    private String protocolNumber;

    @DtoField
    private LocalDate protocolDate;

    @DtoField
    private Boolean verification;

    @DtoField
    private Boolean isDeleted;

    @CreationTimestamp
    private LocalDateTime createdDate;

    @DtoField
    @JsonIgnore
    private Long createdBy;

    /**
     * {@inheritDoc}
     */
    @Override
    public IdentityEntity reach(CrudService crudService) {
        super.reach(crudService);

        AccessTokenPayloadDto tokenPayload = crudService.getTokenPayloadBySystem();
        setCreatedBy(ObjectUtils.defaultIfNull(createdBy,
                Optional.ofNullable(tokenPayload).map(AccessTokenPayloadDto::getStf).orElse(NumberUtils.LONG_ZERO)));

        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void reachTransient(CrudService crudService) {
        super.getMap().put(Attestation.Fields.createdBy, NumberUtils.LONG_ZERO.equals(createdBy)
                ? Constants.SYSTEM : crudService.findTeacherByStaffId(createdBy).getFio());

        super.reachTransient(crudService);
    }

}
