package tech.clink.mesh.porteacher.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import tech.clink.mesh.porteacher.model.common.DeletableTeacherEntity;
import tech.clink.mesh.porteacher.model.ref.SubjectTypeRef;
import tech.clink.mesh.porteacher.service.kafka.dto.NsiEntity;
import tech.clink.mesh.porteacher.util.validation.DtoField;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Data
@Entity
@FieldNameConstants
@Table(name = "subject_teacher")
@EqualsAndHashCode(callSuper = true)
public class Subject extends DeletableTeacherEntity implements NsiEntity {

    @DtoField
    @ManyToOne(cascade = CascadeType.PERSIST)
    private SubjectTypeRef subjectTypeRef;

    private Long nsiId;

    @Override
    @JsonIgnore
    public void setVerification(Boolean verification) {

    }

    @Override
    @JsonIgnore
    public void setCreatedBy(Long createdBy) {

    }

    @Override
    public void setIsDeleted(Boolean isDeleted) {
        if (Boolean.TRUE.equals(isDeleted)) {
            setTeacher(null);
        }
        super.setIsDeleted(isDeleted);
    }
}
