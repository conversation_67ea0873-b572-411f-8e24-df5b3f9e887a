package tech.clink.mesh.porteacher.configuration.datasource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.Objects;
import java.util.function.Supplier;

@Component
@SuppressWarnings("AccessStaticViaInstance")
public class ReadOnlyHolder {
    private static EntityManager entityManager;

    private static ThreadLocal<Boolean> readOnlyHolder = new InheritableThreadLocal<>();

    @Autowired
    public ReadOnlyHolder(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    public static void set(Boolean readOnly) {
        readOnlyHolder.set(readOnly);
    }

    public static Boolean get() {
        return readOnlyHolder.get();
    }

    public static <T> T read(ReadOnlyHolder holder, Supplier<T> supplier) {
        if (Objects.isNull(holder)) return supplier.get();
        set(Boolean.TRUE);
        return holder.read(supplier);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public <T> T read(Supplier<T> supplier) {
        try {
            return supplier.get();
        } finally {
            set(Boolean.FALSE);
        }
    }
}
