package tech.clink.mesh.porteacher.configuration.datasource;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import tech.clink.mesh.porteacher.util.Utils;

import javax.sql.DataSource;
import java.net.InetAddress;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR> on 30.08.2016.
 */
@Data
@Slf4j
@Component
@ConfigurationProperties("spring.datasource")
@ConditionalOnProperty("spring.datasource.replica.enable")
public class DataSourceSettings {
    private String driverClassName;
    private String originUrl;
    private String originPort;
    private String url;
    private String username;
    private String password;
    private Long idleTime;
    private Long maxLifetime;
    private Integer weight = 1;
    private Long connectionTimeout;
    private Integer maximumPoolSize;
    protected Boolean shard = false;
    private DataSourceReplicaProperties property;

    private List<ReplicaDataSourceSettings> replicas = new ArrayList<>();

    public Boolean isValid(DataSourceReplicaProperties property) {
        this.property = property;
        return driverClassName != null
                && (Objects.isNull(property) || property.validate())
                && url != null && username != null && password != null;
    }

    public DataSource buildDataSource(String db) {
        HikariConfig config = createHikariConfig();
        return new HikariDataSource(config) {
            @Override
            public Connection getConnection() throws SQLException {
                log.info(db + " connection from " + url);
//                System.err.println(db + " connection from " + url);
                return super.getConnection();
            }
        };
    }

    protected HikariConfig createHikariConfig() {
        HikariConfig config = new HikariConfig();
        config.setDriverClassName(driverClassName);
        int startIdx = url.indexOf("://") + 3,
                endIdx = url.lastIndexOf(":") > startIdx ? url.lastIndexOf(":") : url.lastIndexOf("/");
        String substring = url.substring(startIdx, endIdx);
        log.info("PoolName {}", substring);
        config.setPoolName("HikariPool-" + substring);
        if (!StringUtils.contains(url, "ApplicationName")) {
            try {
                String hostName = InetAddress.getLocalHost().getHostName();
                log.info("hostName {}", hostName);
                if (!StringUtils.isEmpty(hostName)) {
                    url = url.concat("?ApplicationName=" + hostName);
                }
            } catch (Exception ignored) {
            }
        }
        log.info("success config");
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        Optional.ofNullable(maximumPoolSize).ifPresent(config::setMaximumPoolSize);
        Optional.ofNullable(idleTime).ifPresent(config::setIdleTimeout);
        Optional.ofNullable(connectionTimeout).ifPresent(config::setConnectionTimeout);
        Optional.ofNullable(maxLifetime).ifPresent(config::setMaxLifetime);

        if (Objects.nonNull(property)) {
            for (DataSourceReplicaProperties.DataSourceReplicaProperty property : property.getList()) {
                ReplicaDataSourceSettings replicaDataSourceSettings = new ReplicaDataSourceSettings();
                Utils.copyNonNullProperties(this, replicaDataSourceSettings);
                replicaDataSourceSettings.setProperty(null);

                String tmp = StringUtils.replace(getUrl(), originPort, property.getPort());
                tmp = StringUtils.replace(tmp, originUrl, property.getHost());
                replicaDataSourceSettings.setUrl(tmp);
                replicaDataSourceSettings.setUsername(property.getUser());
                replicaDataSourceSettings.setPassword(property.getPassword());
                replicaDataSourceSettings.setReplicas(Collections.emptyList());
                replicas.add(replicaDataSourceSettings);
            }
        }
        return config;
    }
}
