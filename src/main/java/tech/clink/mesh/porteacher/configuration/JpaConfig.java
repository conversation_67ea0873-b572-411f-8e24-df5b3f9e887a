package tech.clink.mesh.porteacher.configuration;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;
import org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import tech.clink.mesh.porteacher.util.Utils;

import java.util.Collections;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

import static tech.clink.mesh.porteacher.model.ref.RefEntity.REF_PREFIX;

@Configuration
@EnableJpaAuditing
public class JpaConfig extends SpringPhysicalNamingStrategy {
    private static final Set<String> skips = Collections.emptySet();
    private AtomicReference<String> table = new AtomicReference<>();//fixme remove after

    @Override
    public Identifier toPhysicalTableName(Identifier name, JdbcEnvironment jdbc) {
        if (skips.contains(name.getText())) return super.toPhysicalTableName(name, jdbc);
        String tableName = Utils.camel2underscore(name.getText());
        table.set(tableName);
        return Identifier.toIdentifier(tableName, name.isQuoted());
    }

    @Override
    public Identifier toPhysicalColumnName(Identifier name, JdbcEnvironment jdbcEnvironment) {
        String column = name.getText();
        if (column.contains(REF_PREFIX)) {
            name = new Identifier(StringUtils.remove(
                    column, REF_PREFIX), name.isQuoted());
        }
        return super.toPhysicalColumnName(name, jdbcEnvironment);
    }
}
