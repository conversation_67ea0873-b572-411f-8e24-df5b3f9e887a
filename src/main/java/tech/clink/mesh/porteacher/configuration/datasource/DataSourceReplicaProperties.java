package tech.clink.mesh.porteacher.configuration.datasource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR> on 30.08.2016.
 */
@Data
@Component
@ConfigurationProperties("spring.datasource.replica")
public class DataSourceReplicaProperties {
    private List<String> hosts;
    private List<String> ports;
    private List<String> users;
    private List<String> passwords;

    boolean validate() {
        return CollectionUtils.size(ports) == CollectionUtils.size(hosts) &&
                CollectionUtils.size(ports) == CollectionUtils.size(users) &&
                CollectionUtils.size(users) == CollectionUtils.size(passwords);
    }

    public List<DataSourceReplicaProperty> getList() {
        return IntStream.range(0, CollectionUtils.size(hosts))
                .mapToObj(i -> new DataSourceReplicaProperty(
                        hosts.get(i), ports.get(i), users.get(i), passwords.get(i)))
                .collect(Collectors.toList());
    }

    @Getter
    @AllArgsConstructor
    public static class DataSourceReplicaProperty {
        private String host;
        private String port;
        private String user;
        private String password;
    }
}
