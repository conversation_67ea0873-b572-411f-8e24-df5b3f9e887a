package tech.clink.mesh.porteacher.configuration.datasource;

import com.zaxxer.hikari.HikariConfig;

/**
 * <AUTHOR> on 06.09.2016.
 */
public class ReplicaDataSourceSettings extends DataSourceSettings {

    public ReplicaDataSourceSettings(){
        super();
        shard = true;
    }

    @Override
    protected HikariConfig createHikariConfig() {
        HikariConfig hikariConfig = super.createHikariConfig();
        hikariConfig.setReadOnly(true);
        return hikariConfig;
    }
}
