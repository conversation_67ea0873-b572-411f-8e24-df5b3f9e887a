package tech.clink.mesh.porteacher.configuration;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.base.Preconditions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.Nullable;
import org.springframework.validation.beanvalidation.MethodValidationPostProcessor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import tech.clink.mesh.porteacher.util.Utils;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils.uncapitalize;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class AnyConfig extends DefaultResponseErrorHandler {
    @Value("${server.entity}")
    private final Set<String> entities = Collections.emptySet();

    private final ConfigurableApplicationContext ctx;

    @PostConstruct
    @SuppressWarnings("RedundantClassCall")
    private void init() {
        if (entities.contains("ALL")) return; // Монолит
        if (entities.contains(" ")) return; // Монолит
        if (entities.contains("")) return; // Монолит

        Preconditions.checkState(entities.size() == 1);
        String first = entities.iterator().next() + "Controller";

        BeanDefinitionRegistry registry = (BeanDefinitionRegistry) ctx.getAutowireCapableBeanFactory();
        Set<Class<?>> classes = new Reflections("tech.clink.mesh").getTypesAnnotatedWith(RequestMapping.class);
        Set<String> beans = entities.contains("false")
                ? Utils.extract(classes, Class::getName)
                : Utils.extract(classes, Class::getName, s -> !StringUtils.endsWith(s, first));
        for (String controller : beans) {
            if (StringUtils.contains(controller, '$')) {
                Utils.safetyTake(() -> registry.removeBeanDefinition(controller));
            } else {
                Utils.safetyTake(() -> registry.removeBeanDefinition(uncapitalize(
                        StringUtils.substringAfterLast(controller, '.'))));
            }
        }
    }

    @Bean
    public MethodValidationPostProcessor methodValidationPostProcessor() {
        return new MethodValidationPostProcessor();
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplateBuilder()
                .interceptors(new LoggingInterceptor())
                .errorHandler(this)
                .build();
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new Jdk8Module())
                .registerModule(new JavaTimeModule());
        mapper.configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, true);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE, false);
        return mapper;
    }

    @Override
    public void handleError(@Nullable ClientHttpResponse response) {
        // Errors.E711.thr(response.getStatusCode().is2xxSuccessful());
    }

    @Bean
    @ConfigurationProperties(prefix = "external.service.ceds")
    public Map<String, String> chedProperty() {
        return new HashMap<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "click.house.kafka")
    public Map<String, String> clickHouseProperty() {
        return new HashMap<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "access.key")
    public Map<String, String> s3Property() {
        return new HashMap<>();
    }
}
