package tech.clink.mesh.porteacher.configuration.datasource;

import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> <PERSON>
 */
@Slf4j
@Primary
@Component
@ConditionalOnProperty("spring.datasource.replica.enable")
public class EzdRoutingDataSource extends AbstractRoutingDataSource {
    private static ThreadLocal<Integer> currentReplication = new InheritableThreadLocal<>();

    private DataSourceSettings dataSourceSettings;
    private Map<Integer, DataSource> dataSources;
    private List<Integer> shardDataSourcesKeys = new ArrayList<>();
    private WeightedRandom random;
    private DataSourceReplicaProperties property;

    public EzdRoutingDataSource(DataSourceSettings dataSourceSettings, DataSourceReplicaProperties property) {
        this.dataSourceSettings = dataSourceSettings;
        initialize(property);
    }

    private void initialize(DataSourceReplicaProperties property) {
        dataSources = new HashMap<>();
        setTargetDataSources(Collections.unmodifiableMap(dataSources));

        random = new WeightedRandom();

        int indexDs = 1;
        Preconditions.checkState(dataSourceSettings.isValid(property));
        DataSource masterDS = null;
        try {
            masterDS = dataSourceSettings.buildDataSource("Master");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("datasource error: ", e);
        }
        setDefaultTargetDataSource(Objects.requireNonNull(masterDS));
        random.addValue(indexDs, dataSourceSettings.getWeight());
        dataSources.put(indexDs++, masterDS);
        if (dataSourceSettings.getShard()) {
            shardDataSourcesKeys.add(indexDs);
        }

        for (DataSourceSettings replicaSettings : dataSourceSettings.getReplicas()) {
            Preconditions.checkState(replicaSettings.isValid(null));
            random.addValue(indexDs, replicaSettings.getWeight());
            dataSources.put(indexDs++, replicaSettings.buildDataSource("Slave "));
            if (replicaSettings.getShard()) {
                shardDataSourcesKeys.add(indexDs);
            }
        }
    }

    @Override
    public DataSource getResolvedDefaultDataSource() {
        return super.getResolvedDefaultDataSource();
    }

    public Map<Integer, DataSource> getDataSources() {
        return dataSources;
    }

    public List<Integer> getShardDataSourcesKeys() {
        return shardDataSourcesKeys;
    }

    public static void setReadOnly(Boolean useReadOnly) {
        ReadOnlyHolder.set(useReadOnly);
    }

    public static boolean isReadOnly() {
        return BooleanUtils.isTrue(ReadOnlyHolder.get());
    }

    @Override
    protected Object determineCurrentLookupKey() {
        if (isReadOnly()) {
            log.debug("from slave");
            Integer chosen = random.randomValue();
            currentReplication.set(chosen);
            return chosen;
        }
        log.debug("from master");

        //return null means using only master database
        return null;
    }

    public static Integer getCurrentReplica() {
        return currentReplication.get();
    }

    public WeightedRandom getRandom() {
        return random;
    }
}
