package tech.clink.mesh.porteacher.configuration;

import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.Map;

@Configuration
@RequiredArgsConstructor
@ConditionalOnClass(AnyConfig.class)
public class KafkaConfig {
    private final Map<String, String> clickHouseProperty;

    @Bean
    public ProducerFactory<String, String> producerFactoryClickHouse() {
        Map<String, Object> props = Maps.newHashMap();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, clickHouseProperty.get("bootstrap-servers"));
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        if (BooleanUtils.toBoolean(clickHouseProperty.get("security-enable"))) {
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG,
                    clickHouseProperty.get("properties-security-protocol"));
            props.put(SaslConfigs.SASL_MECHANISM, clickHouseProperty.get("properties-sasl-mechanism"));
            props.put(SaslConfigs.SASL_JAAS_CONFIG, clickHouseProperty.get("properties-sasl-jaas-config"));
        }

        return new DefaultKafkaProducerFactory<>(props);
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactoryClickHouse());
    }
}
