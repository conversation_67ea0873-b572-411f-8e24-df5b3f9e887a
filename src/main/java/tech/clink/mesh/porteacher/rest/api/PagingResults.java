package tech.clink.mesh.porteacher.rest.api;

import lombok.Data;
import org.springframework.data.domain.Page;

@Data
public class PagingResults {
    private Integer pageNumber;
    private Integer pageTotal;
    private Integer pageSize;
    private Long totalItemsCount;

    public static PagingResults build(Page<?> page) {
        PagingResults p = new PagingResults();
        p.setTotalItemsCount(page.getTotalElements());
        p.setPageTotal(page.getTotalPages());
        p.setPageNumber(page.getNumber());
        p.setPageSize(page.getSize());
        return p;
    }
}
