package tech.clink.mesh.porteacher.rest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.util.ReflectionUtils;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ResponseStatusException;
import tech.clink.mesh.porteacher.rest.api.Api;
import tech.clink.mesh.porteacher.rest.api.NegativeResponse;
import tech.clink.mesh.porteacher.rest.api.PositiveResponse;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.Utils;
import tech.clink.mesh.porteacher.util.Version;

import javax.security.auth.login.CredentialNotFoundException;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.nio.file.AccessDeniedException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.defaultString;
import static tech.clink.mesh.porteacher.util.Constants.DEF_MSG;
import static tech.clink.mesh.porteacher.util.Utils.underscore2camel;

@Slf4j
@RestController
@RestControllerAdvice
@RequiredArgsConstructor
public class AllController {
    @ExceptionHandler(TransactionSystemException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public NegativeResponse<String> handleException(TransactionSystemException t) {
        Throwable cause = Utils.safetyGet(t::getRootCause);
        if (Objects.nonNull(cause) && ConstraintViolationException.class.isAssignableFrom(cause.getClass())) {
            String params = CollectionUtils.emptyIfNull(
                    ((ConstraintViolationException) cause).getConstraintViolations())
                    .stream()
                    .map(ConstraintViolation::getPropertyPath)
                    .map(Utils::toStr)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(", "));
            if (StringUtils.isNotBlank(params)) return handleException(Errors.E708.thr(params));
        }

        return handleException((Throwable) t);
    }

    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public NegativeResponse<String> handleException(Throwable t) {
        t.printStackTrace();
        Throwable cause = Optional.ofNullable(t.getCause()).orElse(t);
        return Api.negativeResponse("500",
                defaultString(cause.getMessage(), DEF_MSG),
                ExceptionUtils.getStackTrace(cause)
        );
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(CredentialNotFoundException.class)
    public NegativeResponse<String> handleException(CredentialNotFoundException t) {
        return Api.negativeResponse("E706",
                "Введен некорректный логин/пароль",
                ExceptionUtils.getStackTrace(t)
        );
    }

    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ExceptionHandler(SecurityException.class)
    public NegativeResponse<String> handleException(SecurityException t) {
        return Api.negativeResponse("E711",
                "Необходима авторизация в системе",
                ExceptionUtils.getStackTrace(t)
        );
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ResponseStatusException.class)
    public PositiveResponse<String> handleException(ResponseStatusException t) {
        return Api.positiveResponse(Errors.E718.getDescription());
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public NegativeResponse<String> handleException(MissingServletRequestParameterException t) {
        return Api.negativeResponse(Errors.E711.name(),
                StringUtils.replaceOnce(Errors.E711.getDescription(), "%s", t.getParameterName()),
                ExceptionUtils.getStackTrace(t)
        );
    }

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(Errors.CodifiedException.class)
    public NegativeResponse<String> handleException(Errors.CodifiedException t) {
        log.error(t.getMessage(), t);
        return Api.negativeResponse(t.getError().name(),
                t.getMsg(), ExceptionUtils.getStackTrace(
                        ObjectUtils.defaultIfNull(t.getCause(), t))
        );
    }

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(AccessDeniedException.class)
    public NegativeResponse<String> handleException(AccessDeniedException t) {
        return Api.negativeResponse(Errors.E710.name(),
                Errors.E710.getDescription(), t.getClass().getName()
        );
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public NegativeResponse<String> handleException(MethodArgumentNotValidException t) {
//        FieldError fieldError = requireNonNull(t.getBindingResult().getFieldError());
//        String defaultMessage = fieldError.getField() + fieldError.getDefaultMessage();
        FieldError error = t.getBindingResult().getFieldError();
        return Api.negativeResponse(Errors.E707.name(),
                String.format(Errors.E707.getDescription(),
                        Objects.requireNonNull(error).getField()),
                ExceptionUtils.getStackTrace(t)
        );
    }

    @GetMapping("version")
    public Object currentVersion() {
        Map<String, Object> data = Utils.index(Arrays.asList(Version.class.getFields()),
                field -> underscore2camel(field.getName()),
                field -> ReflectionUtils.getField(field, null));
        data.put("now", LocalDateTime.now());
        return data;
    }
}
