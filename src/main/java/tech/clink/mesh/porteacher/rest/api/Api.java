package tech.clink.mesh.porteacher.rest.api;

import org.springframework.data.domain.Page;

public class Api {
    public static <T> PositiveResponse<T> positiveResponse(T data) {
        return new PositiveResponse<>(data);
    }
    public static <T> PositiveResponse<T> positiveResponse(T data, Page<?> page) {
        return new PositiveResponse<>(data).paged(page);
    }

    public static <T> PositiveResponse<T> emptyPositiveResponse() {
        return new PositiveResponse<>(null);
    }

    public static NegativeResponse<String> negativeResponse(String code, String errorMessage, Object details) {
        return new NegativeResponse<>(code, errorMessage, details);
    }

    public static NegativeResponse<Integer> negativeResponse(Integer code, String errorMessage, Object details) {
        return new NegativeResponse<>(code, errorMessage, details);
    }
}
