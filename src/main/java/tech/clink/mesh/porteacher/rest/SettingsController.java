package tech.clink.mesh.porteacher.rest;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.util.CastUtils;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import tech.clink.mesh.porteacher.model.AdministratorSettings;
import tech.clink.mesh.porteacher.model.Teacher;
import tech.clink.mesh.porteacher.model.TeacherSettings;
import tech.clink.mesh.porteacher.model.VisibilitySettingHistory;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.dto.aupd.AccessTokenPayloadDto;
import tech.clink.mesh.porteacher.model.ref.SectionRef;
import tech.clink.mesh.porteacher.rest.api.Api;
import tech.clink.mesh.porteacher.rest.api.PositiveResponse;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.util.Errors;
import tech.clink.mesh.porteacher.util.Utils;
import tech.clink.mesh.porteacher.util.aspect.LoggedMethod;
import tech.clink.mesh.porteacher.util.validation.Secured;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static java.lang.Boolean.FALSE;
import static java.time.LocalTime.MAX;
import static java.time.LocalTime.MIN;
import static tech.clink.mesh.porteacher.model.common.ActionCode.OTHER;
import static tech.clink.mesh.porteacher.model.common.IdentityEntity.Fields.id;
import static tech.clink.mesh.porteacher.util.Constants.ENTITY_CODE;
import static tech.clink.mesh.porteacher.util.Constants.EntityCode;
import static tech.clink.mesh.porteacher.util.Constants.IS_DISABLE;
import static tech.clink.mesh.porteacher.util.Constants.IS_VISIBLE;
import static tech.clink.mesh.porteacher.util.Constants.MAX_DATE;
import static tech.clink.mesh.porteacher.util.Constants.MIN_DATE;
import static tech.clink.mesh.porteacher.util.Constants.SECTIONS;
import static tech.clink.mesh.porteacher.util.Constants.SETTINGS;
import static tech.clink.mesh.porteacher.util.Constants.TEACHER_ID;


@Hidden
@Validated
@RestController
@SuppressWarnings("InnerClassMayBeStatic")
public class SettingsController {
    @Data
    public static class Settings {
        @NotNull
        private JsonNode settings;
        private String reason;
    }

    @Hidden
    @RestController
    @RequiredArgsConstructor
    @RequestMapping("administrator/settings/section")
    public class AdmSettingsController {
        private final CrudService crudService;
        private final ObjectMapper objectMapper;

        @Getter
        private final Class<AdministratorSettings> clazz = AdministratorSettings.class;

        @GetMapping
        @Transactional
        @LoggedMethod(OTHER)
        @Secured(globalRoles = Secured.GlobalRole.ADMIN)
        public PositiveResponse<?> getAdministratorSettings() {
            AdministratorSettings administratorSettings = Utils.safetyGet(
                    () -> crudService.findByLatestDateInNewTransaction(
                            AdministratorSettings.class, Collections.emptyMap()));

            Page<SectionRef> sectionRef = crudService.find(
                    SectionRef.class, Collections.emptyMap(), Pageable.unpaged());
            Errors.E705.thr(sectionRef.hasContent(), SectionRef.class.getSimpleName());

            if (Objects.isNull(administratorSettings)) {
                JsonNode settings = getSettingsBasedOnSectionRef(objectMapper, sectionRef);
                removePersonalInfo(settings.get(SETTINGS));
                return Api.positiveResponse(settings);
            }

            JsonNode sections = administratorSettings.getSettings();
            putAllSettings(objectMapper, sections, sectionRef);
            removePersonalInfo(sections);

            return Api.positiveResponse(administratorSettings);
        }

        @PostMapping
        @LoggedMethod(OTHER)
        @Secured(globalRoles = Secured.GlobalRole.ADMIN)
        public PositiveResponse<AdministratorSettings> create(@Valid @RequestBody Settings settings) {
            // 1
            AdministratorSettings administratorSettings = Utils.safetyGet(
                    () -> crudService.findByLatestDate(
                            AdministratorSettings.class, Collections.emptyMap()));

            if (Objects.isNull(administratorSettings)) {
                administratorSettings = new AdministratorSettings();
            }

            JsonNode settingsSettings = settings.getSettings();
            Map<Long, Pair<Boolean, Long>> difference = difference(
                    administratorSettings.getSettings(), settingsSettings);

            AccessTokenPayloadDto tokenPayload = crudService.getTokenPayloadBySystem();
            Teacher teacher = crudService.findTeacherByStaffId(tokenPayload.getStf());
            crudService.persist(VisibilitySettingHistory.build(
                    difference, settings.getReason(), teacher.getId()));

            administratorSettings.setSettings(settingsSettings);

            return Api.positiveResponse(crudService.save(administratorSettings));
        }

        private Map<Long, Pair<Boolean, Long>> difference(JsonNode left, JsonNode right) {
            Map<Long, Pair<Boolean, Long>> l = new HashMap<>();
            Map<Long, Pair<Boolean, Long>> r = new HashMap<>();
            for (JsonNode sections : right.get(SECTIONS)) {
                r.put(sections.get("id").asLong(),
                        Pair.of(sections.get(IS_VISIBLE).asBoolean(),
                                sections.get("parentId").asLong()));
            }

            if (Objects.isNull(left)) {
                r.values().removeIf(e -> BooleanUtils.isTrue(e.getLeft()));

                return r;
            }

            for (JsonNode sections : left.get(SECTIONS)) {
                l.put(sections.get("id").asLong(),
                        Pair.of(sections.get(IS_VISIBLE).asBoolean(),
                                sections.get("parentId").asLong()));
            }
            MapDifference<Long, Pair<Boolean, Long>> difference = Maps.difference(l, r);

            return Maps.transformValues(difference.entriesDiffering(), MapDifference.ValueDifference::rightValue);
        }
    }

    public static JsonNode getSettingsBasedOnSectionRef(ObjectMapper objectMapper, Page<SectionRef> sectionRef) {
        JsonNode sections = getSectionsBasedOnSectionRef(objectMapper, sectionRef);

        return wrapInSettings(objectMapper, sections);
    }

    public static JsonNode getSectionsBasedOnSectionRef(ObjectMapper objectMapper, Page<SectionRef> sectionRef) {
        return objectMapper.createObjectNode().set(SECTIONS,
                objectMapper.valueToTree(sectionRef.getContent()));
    }

    public static JsonNode wrapInSettings(ObjectMapper objectMapper, JsonNode sections) {
        return objectMapper.createObjectNode().set(SETTINGS, sections);
    }

    public static void putAllSettings(ObjectMapper objectMapper, JsonNode settings, Page<SectionRef> sectionRef) {
        Map<Long, SectionRef> index = Utils.index(sectionRef.getContent(), IdentityEntity::getId);

        ArrayNode sectionList = (ArrayNode) settings.get(SECTIONS);
        for (JsonNode setting : sectionList) {
            index.remove(setting.get("id").asLong());
        }
        sectionList.addAll((ArrayNode) objectMapper.valueToTree(index.values()));
    }

    public static void removePersonalInfo(JsonNode settings) {
        ArrayNode sectionList = (ArrayNode) settings.get(SECTIONS);
        List<JsonNode> sections = StreamSupport.stream(sectionList.spliterator(), FALSE)
                .filter(section -> !EntityCode.ENTITY_CODES.contains(section.get(ENTITY_CODE).asText()))
                .collect(Collectors.toList());

        ((ArrayNode) settings.get(SECTIONS)).removeAll().addAll(sections);
    }

    @Hidden
    @RestController
    @RequiredArgsConstructor
    @RequestMapping("teachers/{teacherId}/settings/section")
    public class TeacherSettingsController implements CheckableController {
        private final CrudService crudService;
        private final ObjectMapper objectMapper;

        @Getter
        private final Class<TeacherSettings> clazz = TeacherSettings.class;

        /**
         * @noinspection ConstantConditions
         */
        @GetMapping
        @Transactional
        @LoggedMethod(OTHER)
        @Secured(globalRoles = Secured.GlobalRole.TEACHER, urlCookie = true)
        public PositiveResponse<JsonNode> getSettings(@PathVariable Long teacherId) {
            // 1
            TeacherSettings teacherSettings = Utils.safetyGet(
                    () -> crudService.findByLatestDateInNewTransaction(TeacherSettings.class,
                            Collections.singletonMap(TEACHER_ID, teacherId)));

            // 2
            AdministratorSettings administratorSettings = Utils.safetyGet(
                    () -> crudService.findByLatestDateInNewTransaction(
                            AdministratorSettings.class, Collections.emptyMap()));

            Page<SectionRef> sectionRef = crudService.find(
                    SectionRef.class, Collections.emptyMap(), Pageable.unpaged());
            Errors.E705.thr(sectionRef.hasContent(), SectionRef.class.getSimpleName());

            // 4 -> 5
            if (Objects.isNull(teacherSettings) && Objects.isNull(administratorSettings)) {
                return Api.positiveResponse(getSettingsBasedOnSectionRef(objectMapper, sectionRef));
            }

            // 4 -> 6
            if (Objects.isNull(teacherSettings) && Objects.nonNull(administratorSettings)) {
                JsonNode settings = administratorSettings.getSettings();
                removePersonalInfo(settings);
                putAllSettings(objectMapper, settings, sectionRef);
                ArrayNode sectionList = (ArrayNode) settings.get(SECTIONS);

                for (JsonNode jsonNode : sectionList) {
                    if (!jsonNode.has(IS_VISIBLE)) {
                        continue;
                    }
                    String isAvailable = jsonNode.get(IS_VISIBLE).asText();
                    if (BooleanUtils.isFalse(BooleanUtils.toBooleanObject(isAvailable))) {
                        ((ObjectNode) jsonNode).put(IS_DISABLE, true);
                    }
                }

                return Api.positiveResponse(wrapInSettings(objectMapper, settings));
            }

            // 4 -> 7
            if (Objects.nonNull(teacherSettings) && Objects.isNull(administratorSettings)) {
                JsonNode settings = teacherSettings.getSettings();
                putAllSettings(objectMapper, settings, sectionRef);

                return Api.positiveResponse(wrapInSettings(objectMapper, settings));
            }

            // 4 -> 8
            if (Objects.nonNull(teacherSettings) && Objects.nonNull(administratorSettings)) {
                JsonNode admSections = administratorSettings.getSettings();
                removePersonalInfo(admSections);
                ArrayNode sectionList = (ArrayNode) admSections.get(SECTIONS);
                Set<Long> sectionIds = new HashSet<>();
                for (JsonNode setting : sectionList) {
                    String isAvailable = setting.get(IS_VISIBLE).asText();
                    if (BooleanUtils.isFalse(BooleanUtils.toBooleanObject(isAvailable))) {
                        sectionIds.add(setting.get("id").asLong());
                    }
                }

                JsonNode settings = teacherSettings.getSettings();
                putAllSettings(objectMapper, settings, sectionRef);
                sectionList = (ArrayNode) settings.get(SECTIONS);
                for (JsonNode setting : sectionList) {
                    if (sectionIds.contains(setting.get("id").asLong())) {
                        ObjectNode s = (ObjectNode) setting;
                        s.put(IS_VISIBLE, false);
                        s.put(IS_DISABLE, true);
                    }
                }

                return Api.positiveResponse(wrapInSettings(objectMapper, settings));
            }

            throw new RuntimeException("Smth went wrong");
        }

        @PostMapping
        @LoggedMethod(OTHER)
        @Secured(globalRoles = Secured.GlobalRole.TEACHER)
        public PositiveResponse<TeacherSettings> create(@PathVariable Long teacherId,
                                                        @Valid @RequestBody JsonNode settings) {
            TeacherSettings teacherSettings = Utils.safetyGet(
                    () -> crudService.findByLatestDate(TeacherSettings.class,
                            Collections.singletonMap(TEACHER_ID, teacherId)));

            if (Objects.isNull(teacherSettings)) {
                teacherSettings = new TeacherSettings();
                teacherSettings.setTeacher(crudService.find(Teacher.class, teacherId));

                Page<SectionRef> sectionRef = crudService.find(
                        SectionRef.class, Collections.emptyMap(), Pageable.unpaged());
                Errors.E705.thr(sectionRef.hasContent(), SectionRef.class.getSimpleName());
                teacherSettings.setSettings(getSectionsBasedOnSectionRef(objectMapper, sectionRef));
            }

            Map<Long, Boolean> oldVisibility = Maps.newHashMap();
            ArrayNode sectionList = CastUtils.cast(teacherSettings.getSettings().get(SECTIONS));
            for (JsonNode setting : sectionList) {
                oldVisibility.put(setting.get(id).asLong(), setting.get(IS_VISIBLE).asBoolean());
            }

            JsonNode inputSettings = settings.get(SETTINGS);
            sectionList = CastUtils.cast(inputSettings.get(SECTIONS));
            for (JsonNode setting : sectionList) {
                ObjectNode s = CastUtils.cast(setting);
                Boolean isDisabled = Utils.safGet(setting.get(IS_DISABLE), JsonNode::asBoolean);
                if (BooleanUtils.isTrue(isDisabled)) {
                    s.put(IS_VISIBLE, oldVisibility.get(s.get(id).asLong()));
                }
                s.put(IS_DISABLE, Boolean.FALSE);
            }

            teacherSettings.setSettings(inputSettings);

            return Api.positiveResponse(crudService.save(teacherSettings));
        }
    }

    @Hidden
    @RestController
    @RequiredArgsConstructor
    @RequestMapping("administrator/settings/history")
    public class VisibilitySettingHistoryController {
        private final CrudService crudService;

        @Getter
        private final Class<VisibilitySettingHistory> clazz = VisibilitySettingHistory.class;

        @GetMapping
        @LoggedMethod(OTHER)
        @Secured(globalRoles = Secured.GlobalRole.ADMIN)
        public PositiveResponse<List<VisibilitySettingHistory>> getVisibilitySettingHistory(
                @RequestParam(required = false)
                        Long sectionId,
                @RequestParam(required = false, defaultValue = MIN_DATE)
                @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
                        LocalDate createdDateFrom,
                @RequestParam(required = false, defaultValue = MAX_DATE)
                @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
                        LocalDate createdDateTo,
                @PageableDefault Pageable pageable) {

            List<VisibilitySettingHistory> historyList;

            Map<String, Object> params = new HashMap<>();
            params.put("dateFrom", createdDateFrom.atTime(MIN));
            params.put("dateTo", createdDateTo.atTime(MAX));

            if (Objects.isNull(sectionId)) {
                historyList = crudService.findHistoryAdminSettingsByDate(
                        clazz, params, getSortingData(pageable));
            } else {
                historyList = crudService.findHistoryAdminSettingsByDate(
                        clazz, sectionId, params, getSortingData(pageable));
            }

            Page<VisibilitySettingHistory> page = new PageImpl<>(
                    historyList.stream()
                            .skip(pageable.getOffset())
                            .limit(pageable.getPageSize())
                            .peek(e -> e.reachTransient(crudService))
                            .collect(Collectors.toList()),
                    pageable,
                    historyList.size()
            );

            return Api.positiveResponse(page.getContent(), page);
        }

        private String getSortingData(Pageable pageable) {
            if (pageable.getSort().isSorted() && pageable.getSort().get().findFirst().isPresent()) {
                Sort.Order order = pageable.getSort().get().findFirst().get();

                if (order.getProperty().equals("id")) {
                    return "vsh.id " + order.getDirection();
                } else if (order.getProperty().equals("sectionName")) {
                    return "sr.name " + order.getDirection();
                } else if (order.getProperty().equals(IS_VISIBLE)) {
                    return "vsh.isVisible " + order.getDirection();
                } else if (order.getProperty().equals("createdBy")) {
                    return "t.surname " + order.getDirection();
                } else if (order.getProperty().equals("createdDate")) {
                    return "vsh.createdDate " + order.getDirection();
                }
            }

            return "vsh.createdDate desc";
        }
    }
}
