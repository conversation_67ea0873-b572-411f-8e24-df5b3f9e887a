package tech.clink.mesh.porteacher.rest.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@Data
@JsonInclude(NON_NULL)
public class ChedCreateDTO {
    private String document;
    private String documentClass;
    private String fromSystemCode;
    private Properties properties;
    private String store;
    private String path;

    @Data
    @Builder
    @JsonInclude(NON_NULL)
    public static class Properties {
        private String ssoid;
        private String documentTitle;
        private String mimeType;
    }
}

