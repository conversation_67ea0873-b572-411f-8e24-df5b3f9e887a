package tech.clink.mesh.porteacher.rest.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.ToString;
import org.springframework.data.domain.Page;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@ToString
public class PositiveResponse<T> extends Response {

    @JsonInclude(NON_NULL)
    private final T data;

    @Getter
    @JsonInclude(NON_NULL)
    private PagingResults pagingResults;

    public PositiveResponse(T data) {
        super(Boolean.TRUE);
        this.data = data;
    }

    public PositiveResponse<T> paged(Page<?> data) {
        this.pagingResults = PagingResults.build(data);
        return this;
    }

    public Boolean getResult() {
        return Boolean.TRUE;
    }

    public T getData() {
        return data;
    }
}
