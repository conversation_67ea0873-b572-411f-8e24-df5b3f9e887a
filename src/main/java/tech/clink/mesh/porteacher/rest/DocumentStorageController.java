package tech.clink.mesh.porteacher.rest;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;
import tech.clink.mesh.porteacher.model.DocumentStorage;
import tech.clink.mesh.porteacher.model.dto.Context;
import tech.clink.mesh.porteacher.rest.api.Api;
import tech.clink.mesh.porteacher.rest.api.PositiveResponse;
import tech.clink.mesh.porteacher.service.DataService;
import tech.clink.mesh.porteacher.util.validation.Secured;

import javax.mail.internet.MimeUtility;
import java.util.Collections;
import java.util.Set;

import static com.google.common.net.HttpHeaders.CONTENT_DISPOSITION;
import static com.google.common.net.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;
import static tech.clink.mesh.porteacher.util.Constants.DOT;
import static tech.clink.mesh.porteacher.util.validation.Secured.GlobalRole.ADMIN;
import static tech.clink.mesh.porteacher.util.validation.Secured.GlobalRole.TEACHER;

@Validated
@RestController
@RequestMapping("ext")
@RequiredArgsConstructor
@ConditionalOnProperty("server.ext.s3.enable")
public class DocumentStorageController {
    @Getter
    private final Class<?> clazz = Context.class;
    private final DataService dataService;

    @RequestMapping(
            path = "documents",
            method = RequestMethod.POST,
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Secured(globalRoles = {ADMIN, TEACHER})
    public PositiveResponse<Object> createS3Document(@RequestPart MultipartFile document) {
        return Api.positiveResponse(dataService.createNewS3Document(document));
    }

    @ApiIgnore
    @PostMapping("document")
    public PositiveResponse<Object> getByUrl(@RequestHeader String authorization,
                                             @RequestParam String mimeType,
                                             @RequestParam String name,
                                             @RequestBody String document) {
        return Api.positiveResponse(dataService.createNewS3Document(document, name, mimeType, authorization));
    }

    @Secured
    @SneakyThrows
    @GetMapping("documents/{documentId}")
    public ResponseEntity<ByteArrayResource> download(@RequestHeader String authorization,
                                                      @PathVariable Long documentId) {
        DocumentStorage documentStorage = dataService.download(documentId);
        String format = documentStorage.getFormat();
        String contentType = "application/".concat(format);
        String fileName = documentStorage.getName();
        fileName = MimeUtility.encodeWord(fileName, "utf-8", "Q");
        fileName = fileName.concat(DOT).concat(format);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_TYPE, contentType)
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + fileName + "\"")
                .body(new ByteArrayResource(documentStorage.getBody()));
    }

    @GetMapping("getcontent")//getcontent?os=DIT&id=
    public ResponseEntity<ByteArrayResource> getByUrl(@RequestParam String id) {
        DocumentStorage storage = dataService.download(id);
        String name = storage.getName().concat(DOT).concat(storage.getFormat());

        return ResponseEntity.ok()
                .header(CONTENT_TYPE, APPLICATION_OCTET_STREAM_VALUE)
                .header(CONTENT_DISPOSITION, "attachment; filename=" + name)
                .body(new ByteArrayResource(storage.getBody()));
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DeleteMapping("documents/{documentId}")
    @Secured(globalRoles = {ADMIN, TEACHER})
    public void drop(@RequestHeader String authorization,
                     @PathVariable Long documentId) {
        dataService.delete(Collections.singleton(documentId));
    }

    @DeleteMapping("documents")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Secured(globalRoles = {ADMIN, TEACHER})
    public void drop(@RequestHeader String authorization,
                     @RequestParam Set<Long> documentId) {
        dataService.delete(documentId);
    }

    @DeleteMapping("documents/force")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Secured(globalRoles = {ADMIN, TEACHER})
    public void delete(@RequestHeader String authorization,
                       @PageableDefault Pageable pageable) {
        dataService.delete(pageable);
    }
}
