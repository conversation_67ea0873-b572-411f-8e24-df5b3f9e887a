package tech.clink.mesh.porteacher.rest.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.experimental.FieldNameConstants;

@Getter
@FieldNameConstants
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NegativeResponse<T> extends Response {

    @JsonProperty(index = 1)
    protected final T code;

    @JsonProperty(index = 2)
    private final String message;

    @JsonProperty(index = 3)
    private final Object details;

    NegativeResponse(T code, String message, Object details) {
        super(Boolean.FALSE);
        this.code = code;
        this.message = message;
        this.details = details;
    }

    public Boolean getResult() {
        return Boolean.FALSE;
    }
}
