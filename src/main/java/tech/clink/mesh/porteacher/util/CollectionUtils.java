package tech.clink.mesh.porteacher.util;

import org.apache.commons.collections4.IterableUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;

public abstract class CollectionUtils {
    public static <T, R> Set<R> extract(Collection<T> source, Function<T, R> extractor) {
        return extract(source, extractor, r -> true);
    }

    public static <T, R> Set<R> extract(Collection<T> source, Function<T, R> extractor, Predicate<R> valuePredicate) {
        return extract(source, extractor, valuePredicate, toSet());
    }

    public static <T, R, C extends Iterable<R>> C extract(Collection<T> source, Function<T, R> extractor,
                                                          Predicate<R> valuePredicate, Collector<R, ?, C> collector) {
        return emptyIfNull(source).stream().map(extractor).filter(valuePredicate).collect(collector);
    }

    public static <T, R, E, C extends Iterable<E>> C extract(Collection<T> source, Function<T, R> extractor,
                                                             Predicate<R> valuePredicate, Function<R, E> adapter,
                                                             Collector<E, ?, C> collector) {
        return emptyIfNull(source).stream().map(extractor).filter(valuePredicate).map(adapter).collect(collector);
    }

    public static <K, V> Map<K, V> index(Collection<V> collection, Function<V, K> converter) {
        return index(collection, converter, Function.identity());
    }

    public static <K, V, E> Map<K, E> index(Collection<V> collection, Function<V, K> converter, Function<V, E> valueMapper) {
        return emptyIfNull(collection).stream().collect(Collectors.toMap(converter, valueMapper, (x, y) -> x));
    }

    public static <K, V, E> Map<K, E> index(Collection<V> collection, Function<V, K> converter,
                                            Function<V, E> valueMapper, Predicate<V> predicate) {
        return emptyIfNull(collection).stream().filter(predicate)
                .collect(Collectors.toMap(converter, valueMapper, (x, y) -> x));
    }

    public static <T, R> List<R> transform(Collection<T> source, Function<T, R> extractor) {
        return transform(source, extractor, r -> true);
    }

    public static <T, R> List<R> iterable(Iterable<? extends T> source, Function<T, R> extractor) {
        return StreamSupport.stream(IterableUtils.emptyIfNull(source).spliterator(), false)
                .map(extractor).collect(Collectors.toList());
    }

    public static <T,R> List<T> filter(Collection<T> source, Function<T, R> keyExtractor, Predicate<R>predicate) {
        return transform(source, Function.identity(), p -> predicate.test(keyExtractor.apply(p)));
    }

    public static <T, R> List<R> transform(Collection<T> source, Function<T, R> extractor, Predicate<R> predicate) {
        return extract(source, extractor, predicate, toList());
    }

    public static boolean setIsValid(Set<Long> set) {
        return !set.remove(null) && !set.isEmpty();
    }
}
