package tech.clink.mesh.porteacher.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Getter
@RequiredArgsConstructor
public enum Errors {
    E701("Отсутствует токен АУПД"),
    E702("Ошибка верификации токена"),
    E703("Время действия токена еще не началось"),
    E704("Доступ к запрошенному ресурсу запрещен"),
    E705("Внутренняя ошибка %s"),
    E706("Время действия токена истекло"),
    E707("Сущность %s по идентификатору не найдена"),
    E708("Отсутствуют обязательные параметры: %s"),
    E709("Размер документа превышает максимально допустимый 20МБ"),
    E710("Недопустимый формат документа"),
    E711("Некорректные входные данные"),
    E712("Сущность %s по идентификатору %s не принадлежит данному учителю"),
    E713("Сущность %s по идентификатору %s удалена"),
    E714("Запрещено редактировать данную запись"),
    E715("Загруженный документ уже принадлежит существующей сущности"),
    E716("Невозможно создать повторяющийся объект"),
    E717("Ссылка недействительна"),
    E718("По вашему запросу найдено более одного учителя. Пожалуйста, конкретизируйте запрос");

    private final String description;

    public CodifiedException thr(Object... args) {
        return new CodifiedException(this, String.format(this.description, args));
    }

    public <T> void thr(Runnable runnable, Object... args) {
        try {
            runnable.run();
        } catch (Throwable e) {
            throw new CodifiedException(this, String.format(this.description, args), e);
        }
    }

    public <T> T thr(Utils.Supplier<T, Throwable> runnable, Object... args) {
        try {
            return runnable.get();
        } catch (Throwable e) {
            throw new CodifiedException(this, String.format(this.description, args), e);
        }
    }

    public void thr(Boolean isTrue, Object... args) {
        if (Boolean.TRUE.equals(isTrue)) return;
        throw new CodifiedException(this, String.format(this.description, args));
    }

    @Getter
    @AllArgsConstructor
    @RequiredArgsConstructor
    public static class CodifiedException extends RuntimeException {
        private final Errors error;
        private String msg;

        public CodifiedException(Errors error, String message, Throwable cause) {
            super(message, cause);
            this.msg = message;
            this.error = error;
        }

        public String getMsg() {
            return StringUtils.defaultString(msg, error.getDescription());
        }

        @Override
        public String getMessage() {
            return getMsg();
        }
    }
}
