package tech.clink.mesh.porteacher.util;

import com.fasterxml.jackson.databind.util.ClassUtil;
import com.google.common.collect.BiMap;
import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.lang.Nullable;
import tech.clink.mesh.porteacher.model.*;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.common.ReachableEntity;
import tech.clink.mesh.porteacher.model.ref.RefEntity;
import tech.clink.mesh.porteacher.service.CrudService;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.temporal.Temporal;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.google.common.base.CaseFormat.LOWER_CAMEL;
import static com.google.common.base.CaseFormat.LOWER_UNDERSCORE;
import static java.util.Collections.emptySet;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.lang3.StringUtils.substringAfter;
import static tech.clink.mesh.porteacher.util.Constants.UTC_ZONE;

@Slf4j
public class Utils {
    public static final Pageable page = PageRequest.of(
            NumberUtils.INTEGER_ZERO, NumberUtils.INTEGER_ONE,
            Sort.Direction.ASC, IdentityEntity.Fields.id
    );

    public static Map<Class<?>, String> CLASS_ORDER = new HashMap<>() {{
        put(Event.class, Event.Fields.date);
        put(Project.class, Project.Fields.endDate);
        put(Award.class, Award.Fields.assignmentDate);
        put(PlaceWork.class, PlaceWork.Fields.endDate);
        put(ShareLink.class, ShareLink.Fields.createdDate);
        put(Education.class, Education.Fields.docIssueDate);
        put(AcademicTitle.class, AcademicTitle.Fields.issueDate);
        put(AcademicDegree.class, AcademicDegree.Fields.issueDate);
        put(Attestation.class, Attestation.Fields.attestationDate);
        put(GratitudeStudent.class, GratitudeStudent.Fields.createdDate);
        put(TeacherDiagnostic.class, TeacherDiagnostic.Fields.diagnosticDate);
    }};

    public static Map<Class<?>, Sort.Direction> CLASS_ORDER_DIR = ImmutableMap.of();

    public static void clear(Map<String, Object> map) {
        map.remove("size");
        map.remove("sort");
        map.remove("page");
    }

    public static String cap(String key) {
        return underscore2camel(StringUtils.replaceChars(key, '.', '_'));
    }

    @FunctionalInterface
    public interface Supplier<T, E extends Throwable> {
        T get() throws E;
    }

    public static <V, T> T safGet(V v, Function<V, T> f) {
        return Optional.ofNullable(v).map(f).orElse(null);
    }

    @Nullable
    public static <V> V safetyGet(Supplier<V, Exception> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.error("smth went wrong, ", e);
            return null;
        }
    }

    public static <V> V getWithError(Supplier<V, Exception> supplier, Errors error) {
        try {
            return supplier.get();
        } catch (Errors.CodifiedException e) {
            throw e;
        } catch (Exception e) {
            throw new Errors.CodifiedException(error);
        }
    }

    public static String getAudRole(String aud) {
        return substringAfter(aud, ":");
    }

    @Nullable
    public static <V> String toStr(V v, Function<V, String> f) {
        return Optional.ofNullable(v).map(f).orElse(null);
    }

    @Nullable
    public static <V> String toStr(V v) {
        return toStr(v, String::valueOf);
    }

    public static void safetyTake(Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("smth went wrong, ", e);
        }
    }

    public static <K, V, E> BiMap<K, E> bimap(Collection<V> collection, Function<V, K> converter, Function<V, E> valueMapper) {
        return ImmutableBiMap.copyOf(index(collection, converter, valueMapper));
    }

    public static <K, V> Map<K, V> index(Collection<V> collection, Function<V, K> converter) {
        return index(collection, converter, Function.identity());
    }

    public static <K, V, E> Map<K, E> index(Collection<V> collection, Function<V, K> converter, Function<V, E> valueMapper) {
        return CollectionUtils.emptyIfNull(collection).stream().collect(Collectors.toMap(converter, valueMapper));
    }

    public static <T, R> Set<R> extract(Collection<T> source, Function<T, R> extractor) {
        return extract(source, extractor, r -> true);
    }

    public static <T, R> Set<R> extract(Collection<T> source, Function<T, R> extractor, Predicate<R> valuePredicate) {
        return extract(source, extractor, valuePredicate, toSet());
    }

    public static <T, R> List<R> transform(Collection<T> source, Function<T, R> extractor) {
        return extract(source, extractor, t -> true, toList());
    }

    public static <T, R, C extends Iterable<R>> C extract(Collection<T> source, Function<T, R> extractor,
                                                          Predicate<R> valuePredicate, Collector<R, ?, C> collector) {
        return extract(source, r -> true, extractor, valuePredicate, collector);
    }

    public static <T, R, C extends Iterable<R>> C extract(Collection<T> source,
                                                          Predicate<T> sourcePredicate,
                                                          Function<T, R> extractor,
                                                          Predicate<R> valuePredicate,
                                                          Collector<R, ?, C> collector) {
        return extract(source, sourcePredicate, extractor, valuePredicate, Function.identity(), collector);
    }

    public static <T, R, RR, C extends Iterable<RR>> C extract(Collection<T> source,
                                                               Predicate<T> sourcePredicate,
                                                               Function<T, R> extractor,
                                                               Predicate<R> valuePredicate,
                                                               Function<R, RR> extractorToR,
                                                               Collector<RR, ?, C> collector) {
        return CollectionUtils.emptyIfNull(source).stream()
                .filter(sourcePredicate)
                .map(extractor)
                .filter(valuePredicate)
                .map(extractorToR)
                .collect(collector);
    }

    public static String underscore2camel(String text) {
        return Objects.isNull(text) ? null : LOWER_UNDERSCORE.to(LOWER_CAMEL, text);
    }

    public static String camel2underscore(String text) {
        return Objects.isNull(text) ? null : LOWER_CAMEL.to(LOWER_UNDERSCORE, text);
    }

    public static void copyNonNullProperties(Object src, Object target, String... ignoredFields) {
        copyProperties(src, target, emptySet(), Boolean.TRUE, ignoredFields);
    }

    public static <T> T copyNonNullPropertiesAndGet(Object src, T target, String... ignoredFields) {
        copyNonNullProperties(src, target, ignoredFields);
        return target;
    }

    public static void copyProperties(Object src, Object target, Set<String> mustCopyFields, boolean isPartial, String... ignoredFields) {
        if (Objects.isNull(src)) return;
        BeanWrapper wrappedSrc = new BeanWrapperImpl(src);
        PropertyDescriptor[] propertyDescriptors = wrappedSrc.getPropertyDescriptors();
        Set<String> emptyNames = Sets.newHashSet(ignoredFields);
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            Object srcValue = null;
            String fieldName = propertyDescriptor.getName();
            if (emptyNames.contains(fieldName)) continue;
            try {
                srcValue = wrappedSrc.getPropertyValue(fieldName);
            } catch (Exception ignored) {
            }
            if (Objects.isNull(srcValue) || isDeepCopyingNeeded(srcValue)) {
                emptyNames.add(fieldName);
            }
            if (Objects.nonNull(srcValue) && isDeepCopyingNeeded(srcValue)) {
                try {
                    Field targetField = FieldUtils.getField(target.getClass(), fieldName, Boolean.TRUE);
                    Object targetValue = targetField.get(target);
                    if (Objects.nonNull(targetValue) && ObjectUtils.notEqual(srcValue, targetValue)) {
                        if (isPartial) {
                            copyNonNullProperties(srcValue, targetValue);
                        } else {
                            BeanUtils.copyProperties(srcValue, targetValue);
                        }
                    } else {
                        targetField.set(target, srcValue);
                    }
                } catch (Exception ignored) {
                }
            }
        }

        emptyNames.removeAll(mustCopyFields);
        String[] result = new String[emptyNames.size()];
        BeanUtils.copyProperties(src, target, emptyNames.toArray(result));
    }

    public static Long countNonNullProperties(IdentityEntity o) {
        if (Objects.isNull(o)) return NumberUtils.LONG_ZERO;

        BeanWrapper wrappedSrc = new BeanWrapperImpl(o);
        PropertyDescriptor[] propertyDescriptors = wrappedSrc.getPropertyDescriptors();

        return Arrays.stream(propertyDescriptors)
                .filter(x -> !"class".equals(x.getName()) && !ReachableEntity.Fields.map.equals(x.getName()))
                .filter(x -> Objects.nonNull(wrappedSrc.getPropertyValue(x.getName())))
                .count() + o.getMap().size();
    }

    public static Boolean isDeepCopyingNeeded(Object object) {
        if (ClassUtil.isEnumType(object.getClass())) return false;
        return object.getClass().getName().startsWith(Constants.PACK) &&
                !IdentityEntity.class.isAssignableFrom(object.getClass());
    }

    public static Temporal date(Object o, Boolean from, Class<?> isDateTime) {
        if (isDateTime.isAssignableFrom(ZonedDateTime.class)) return date(o, from);
        if (isDateTime.isAssignableFrom(LocalDateTime.class))
            return Utils.safGet(date(o, from), ZonedDateTime::toLocalDateTime);
        return Utils.safGet(date(o, from), ZonedDateTime::toLocalDate);//fixme
    }

    private static ZonedDateTime date(Object o, Boolean from) {
        if (Objects.isNull(o)) {
            if (Objects.isNull(from)) return null;
            LocalDateTime res;
            if (from) {
                res = LocalDateTime.of(LocalDate.ofYearDay(1900, 1), LocalTime.MIN);
            } else {
                res = LocalDateTime.of(LocalDate.ofYearDay(2100, 1), Constants.MAX);
            }
            return ZonedDateTime.of(res, UTC_ZONE);
        }
        String text = o.toString();
        if (StringUtils.containsAny(text, "Z[]+")) {
            return ZonedDateTime.parse(text);
        }
        if (StringUtils.contains(text, 'T')) {
            return ZonedDateTime.of(LocalDateTime.parse(text), UTC_ZONE);
        }
        if (Objects.isNull(from)) {
            return ZonedDateTime.of(LocalDate.parse(text), LocalTime.MIDNIGHT, UTC_ZONE);
        }
        if (from) {
            return ZonedDateTime.of(LocalDate.parse(text), LocalTime.MIN, UTC_ZONE);
        } else {
            return ZonedDateTime.of(LocalDate.parse(text), Constants.MAX, UTC_ZONE);
        }
    }

    public static String lowerLike(Object value) {
        return StringUtils.wrap(StringUtils.lowerCase(value.toString()), '%');
    }

    public static String join(Collection<?> ids) {
        return CollectionUtils.emptyIfNull(ids)
                .stream().filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.joining(","));
    }

    public static String joinWithLine(Collection<?> ids) {
        return CollectionUtils.emptyIfNull(ids)
                .stream().filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.joining("|"));
    }

    public static <ID> Set<ID> splitWithLineTo(String ids, Function<String, ID> adapter) {
        return Stream.of(StringUtils.split(StringUtils.defaultString(ids), '|'))
                .filter(Objects::nonNull).map(String::trim).map(adapter)
                .collect(Collectors.toSet());
    }

    public static Set<Long> splitToIds(String ids) {
        return splitTo(ids, Long::valueOf);
    }

    public static Collection<String> split(Object ids) {
        return splitWithLineTo(String.valueOf(ids), String::valueOf);
    }

    public static Set<Long> splitToIds(Object ids) {
        return splitTo(String.valueOf(ids), Long::valueOf);
    }

    public static <ID> Set<ID> splitTo(String ids, Function<String, ID> adapter) {
        return Stream.of(StringUtils.split(StringUtils.defaultString(ids), ','))
                .filter(Objects::nonNull).map(String::trim).map(adapter)
                .collect(Collectors.toSet());
    }

    public static <R extends RefEntity> void reachRefs2map(Map<String, Object> map, String ids,
                                                           Class<R> refClass, CrudService crudService) {
        List<R> allRefs = crudService.findAllRefs(splitTo(ids, Integer::valueOf), refClass);
        if (CollectionUtils.isNotEmpty(allRefs)) {
            String key = refClass.getSimpleName();
            key = StringUtils.substringBefore(key, "Ref");
            key = StringUtils.appendIfMissing(key, "s");
            key = StringUtils.uncapitalize(key);
            map.put(key, allRefs);
        }
    }

    public static <E extends ReachableEntity> Pageable getPageableWithDefaultSortIfNeeded(Class<E> clazz,
                                                                                          Pageable pageable) {
        if (pageable.getSort().isUnsorted()) {
            pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
                    Sort.by(CLASS_ORDER_DIR.getOrDefault(clazz, Sort.Direction.DESC),
                            CLASS_ORDER.getOrDefault(clazz, IdentityEntity.Fields.id)));
        }

        return pageable;
    }

    public static <E, T> T extract(Function<E, T> extractor, E ep, E or, Boolean isPartial) {
        T newValue = extractor.apply(or);
        return !BooleanUtils.toBoolean(isPartial) ? newValue :
                ObjectUtils.defaultIfNull(newValue, extractor.apply(ep));
    }

    public static void putAdditionalParamsIfNecessary(Map<String, Object> target, Map<String, Object> source) {
        source.forEach((key, value) -> target.put(key, ObjectUtils.defaultIfNull(target.get(key), value)));
    }
}
