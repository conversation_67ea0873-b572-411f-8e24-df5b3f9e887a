package tech.clink.mesh.porteacher.util.aspect;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Aspect
@Component
@RequiredArgsConstructor
public class ConditionalOnAspect {
    private final Environment environment;

    @Pointcut("@within(tech.clink.mesh.porteacher.util.aspect.ConditionalOn)")
    public void conditionalOn() {

    }

    @Around("conditionalOn()")
    public Object check(ProceedingJoinPoint joinPoint) throws Throwable {
        ConditionalOn conditionalOn = joinPoint.getTarget().getClass().getAnnotation(ConditionalOn.class);
        String property = environment.getProperty(conditionalOn.value());

        if (BooleanUtils.isNotTrue(BooleanUtils.toBooleanObject(property))) {
            return null;
        }

        return joinPoint.proceed();
    }
}
