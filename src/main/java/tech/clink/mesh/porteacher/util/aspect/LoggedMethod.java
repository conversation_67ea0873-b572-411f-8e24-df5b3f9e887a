package tech.clink.mesh.porteacher.util.aspect;

import tech.clink.mesh.porteacher.model.common.ActionCode;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LoggedMethod {
    ActionCode value() default ActionCode.NONE;
}

