package tech.clink.mesh.porteacher.util.validation;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;
import java.util.Optional;

public class DtoFieldValidator implements ConstraintValidator<DtoField, Object> {
    private DtoField dtoAnnotation;

    @Override
    public void initialize(DtoField constraintAnnotation) {
        dtoAnnotation = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        boolean isValid = dtoAnnotation.should().getExist() && Objects.nonNull(value)//поле должно быть задано
                || !dtoAnnotation.should().getExist() && Objects.isNull(value);//поле должно отсутствовать

        String errMsg = StringUtils.EMPTY;
        //проверяем также на NotEmpty для коллекций и строк, если они заданы
        if (Optional.ofNullable(value).flatMap(ValidationUtils::validateCollectionAndString).orElse(isValid)) {
            errMsg = Optional.ofNullable(value).filter(v -> SupportsValidation.class.isAssignableFrom(v.getClass()))
                    .map(SupportsValidation.class::cast).map(SupportsValidation::validate).orElse(errMsg);
            if (StringUtils.isEmpty(errMsg) && isValid) {
                return true;
            }
        }

        //заменяем только параметр "должен *". Название параметра достанем на этапе handle exception
        String msg = StringUtils.defaultIfEmpty(errMsg, String.format(
                dtoAnnotation.message(), dtoAnnotation.should().getDescription(), "%s"));
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(msg).addConstraintViolation();// Добавляем сообщение

        return false;
    }
}
