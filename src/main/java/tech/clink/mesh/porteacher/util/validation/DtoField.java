package tech.clink.mesh.porteacher.util.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DtoFieldValidator.class)
public @interface DtoField {
    Status should() default Status.EXIST;

    String message() default "Параметр %2$s у новых объектов должен %1$s.";

    Class<? extends Payload>[] payload() default {};//поле нужно для валидатора

    Class<?>[] groups() default {};//поле нужно для валидатора
}
