package tech.clink.mesh.porteacher.util.validation;

import com.google.common.collect.ImmutableBiMap;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;

@SuppressWarnings("rawtypes")
public class ValidationUtils {
    private static final Map<String, Predicate> validationMap;

    static {
        Predicate<List> listIsEmpty = Collection::isEmpty;
        Predicate<Set> setIsEmpty = Collection::isEmpty;
        Predicate<Map> mapIsEmpty = Map::isEmpty;
        Predicate<String> isBlank = StringUtils::isBlank;

        validationMap = ImmutableBiMap.of(
                String.class.getSimpleName(), isBlank.negate(),
                HashMap.class.getSimpleName(), mapIsEmpty.negate(),
                HashSet.class.getSimpleName(), setIsEmpty.negate(),
                ArrayList.class.getSimpleName(), listIsEmpty.negate()
        );
    }

    @SuppressWarnings("unchecked")
    public static Optional<Boolean> validateCollectionAndString(Object value) {
        return Optional.ofNullable(validationMap.get(value.getClass().getSimpleName()))
                .map(fun -> fun.test(value));// применяем проверяющие функции
    }


}
