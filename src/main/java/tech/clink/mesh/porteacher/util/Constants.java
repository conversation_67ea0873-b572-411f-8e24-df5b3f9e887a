package tech.clink.mesh.porteacher.util;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import tech.clink.mesh.porteacher.model.Teacher;
import tech.clink.mesh.porteacher.model.VisibilitySettingHistory;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.ref.SectionRef;

import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;

import static java.util.stream.Collectors.toSet;

public final class Constants {
    public static final String NULL = "null";
    public static final String SLASH = "/";
    public static final String DOT = ".";
    public static final String COMMA = ",";
    public static final String[] BETWEEN = ArrayUtils.toArray("From", "To");
    public static final LocalTime MAX = LocalTime.of(23, 59, 59, 59);
    public static final String MIN_DATE = "1900-01-01";
    public static final String MAX_DATE = "2100-12-31";
    public static final String PACK = "tech.clink.mesh.porteacher.model.";
    public static final String HEADER_X_API_KEY = "x-api-key";
    public static final String ID = StringUtils.capitalize(IdentityEntity.Fields.id);
    public static final String ENTITY_CODE = "entityCode";
    public static final String ENTITY_ID = "entityId";
    public static final String TEACHER_ID = "teacher.id";
    public static final String TEACHER__ID = "teacherId";
    public static final String SYSTEM = "Система";
    public static final String WITH_AWARD = "withAward";
    public static final String EVENT_CODE_ID = "eventCodeId";
    public static final String REF = "ref";
    public static final String DEF_MSG = "Системная ошибка";
    public static final String SHARE_TOKEN = "shareToken";
    public static final String AUPD_CURRENT_ROLE = "aupd_current_role";
    public static final String TOTAL_RECORDS = "totalRecords";
    public static final String IS_DISABLE = "isDisable";
    public static final String IS_VISIBLE = "isVisible";
    public static final String SECTIONS = "sections";
    public static final String SETTINGS = "settings";
    public static final String MAIN_PLACE_OF_WORK = "Основное место работы";
    // todo проверить, что именно приходит из топика в качестве этих 4-х типов ↓
    public static final String HIGHLY_QUALIFIED = "высшее - подготовка кадров высшей квалификации";
    public static final String MASTER = "высшее образование - специалитет, магистратура";
    public static final String BACHELOR = "высшее образование - бакалавриат";
    public static final String SECONDARY = "среднее профессиональное образование";

    public static String between(String field) {
        String f = field;
        f = StringUtils.removeEnd(f, BETWEEN[0]);
        f = StringUtils.removeEnd(f, BETWEEN[1]);
        return f;
    }

    public static final String AWARD_SELECT = "select e ";
    public static final String AWARD_GROUP = " group by e.awardTypeRef.name";//fixme mb id
    public static final String AWARD_GROUP_COUNT = " select e.awardTypeRef.name as name, count(e.awardTypeRef.name) as count ";
    public static final String GRAD_GROUP = " group by e.gratitudeCategory";//fixme mb id
    public static final String GRAD_GROUP_COUNT = " select e.gratitudeCategory as name, count(e.gratitudeCategory) as count ";
    public static final String DIAGNOSTIC_GROUP = " group by e.diagnosticLevel";//fixme mb id
    public static final String DIAGNOSTIC_GROUP_COUNT = " select e.diagnosticLevel as name, count(e.diagnosticLevel) as count ";

    public static final ZoneId UTC_ZONE = ZoneOffset.UTC;

    public static final DateTimeFormatter EXCEL_DATE_FORMAT = DateTimeFormatter.ofPattern("dd.MM.yyyy");
    public static final DateTimeFormatter EXCEL_DT_FORMAT = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm");

    public static final TypeReference<Map<String, Object>> MAP_TYPE_REFERENCE = new TypeReference<>() {};

    public static final String VISIBILITY_SETTING_HISTORY_QUERY =
            "select vsh from " + VisibilitySettingHistory.class.getSimpleName() + " vsh " +
                    "left join " + SectionRef.class.getSimpleName() + " sr on vsh.sectionId = sr.id " +
                    "left join " + SectionRef.class.getSimpleName() + " sr2 on sr.parentId = sr2.id " +
                    "left join " + Teacher.class.getSimpleName() + " t on vsh.createdBy = t.id " +
                    "where (vsh.sectionId = :sectionId or sr.parentId = :sectionId or sr2.parentId = :sectionId) " +
                    "and vsh.createdDate between :dateFrom and :dateTo " +
                    "order by ";

    public static final String VISIBILITY_SETTING_HISTORY_QUERY_ALL_SECTIONS =
            "select vsh from " + VisibilitySettingHistory.class.getSimpleName() + " vsh " +
                    "left join " + SectionRef.class.getSimpleName() + " sr on vsh.sectionId = sr.id " +
                    "left join " + Teacher.class.getSimpleName() + " t on vsh.createdBy = t.id " +
                    "where vsh.createdDate between :dateFrom and :dateTo " +
                    "order by ";

    @AllArgsConstructor
    public enum EntityCode {
        MOBILE("Mobile"),
        EMAIL("Email"),
        DOB("Dob");

        public static final Set<String> ENTITY_CODES = Arrays.stream(values())
                .map(EntityCode::getCode).collect(toSet());

        @Getter
        private final String code;
    }

    public static class Views {

        public static class Credo {

        }

        public static class BlockAboutMe {

        }

    }
}
