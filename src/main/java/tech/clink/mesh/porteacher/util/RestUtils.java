package tech.clink.mesh.porteacher.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.Optional;

import static java.util.Collections.singletonList;
import static tech.clink.mesh.porteacher.util.Constants.HEADER_X_API_KEY;

@SuppressWarnings("all")
public class RestUtils {

    public static <T> HttpEntity<T> createAupdHeaderEntity(T body, String app, String token) {
        return createAupdHeaderEntity(body, token, app, MediaType.APPLICATION_JSON);
    }

    public static <T> HttpEntity<T> header(T body, String app, String key) {
        return createHeaderEntity(body, app, key, MediaType.APPLICATION_JSON);
    }

    public static <T> HttpEntity<T> createJSONCEDSHeaderEntity(T body, String systemCode, String password) {
        return createCEDSHeaderEntity(body, systemCode, password, MediaType.APPLICATION_JSON, MediaType.APPLICATION_JSON);
    }

    public static <T> HttpEntity<T> createAllCEDSHeaderEntity(T body, String systemCode, String password) {
        return createCEDSHeaderEntity(body, systemCode, password, MediaType.ALL, MediaType.APPLICATION_OCTET_STREAM);
    }

    public static <T> HttpEntity<T> createCEDSHeaderEntity(T body, String systemCode, String password, MediaType mediaType, MediaType mediaTypeContent) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(mediaTypeContent);
        headers.setAccept(singletonList(mediaType));

        headers.add("Authorization", "Basic " +
                new String(Base64.getEncoder().encode(systemCode.concat(":").concat(password).getBytes())));
        return new HttpEntity<>(body, headers);
    }

    public static <T> HttpEntity<T> createBasicHeaderEntity(T body, String login, String password, String key) {
        return createBasicHeaderEntity(body, Base64.getEncoder().encodeToString(
                login.concat(":").concat(password).getBytes()),
                MediaType.APPLICATION_JSON, MediaType.APPLICATION_JSON, key);
    }

    public static <T> HttpEntity<T> createBasicHeaderEntity(T body, String auth,
                                                            MediaType mediaType,
                                                            MediaType mediaTypeContent,
                                                            String key) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(mediaTypeContent);
        headers.setAccept(singletonList(mediaType));

        headers.add("Authorization", "Basic " + auth);
        Optional.ofNullable(key).filter(StringUtils::isNotBlank)
                .ifPresent(b -> headers.add(HEADER_X_API_KEY, b));
        return new HttpEntity<>(body, headers);
    }

    private static <T> HttpEntity<T> createAupdHeaderEntity(T body, String token, String app, MediaType mediaType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(singletonList(mediaType));
        Optional.ofNullable(app).filter(StringUtils::isNotBlank).ifPresent(b -> headers.set(HEADER_X_API_KEY, b));
        Optional.ofNullable(token).filter(StringUtils::isNotBlank).ifPresent(b -> headers.set("Authorization", b));
        return new HttpEntity<>(body, headers);
    }

    private static <T> HttpEntity<T> createHeaderEntity(T body, String app, MediaType mediaType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(singletonList(mediaType));
        Optional.ofNullable(app).filter(StringUtils::isNotBlank)
                .ifPresent(b -> headers.set("Esz-Access-Token", b));
        Optional.ofNullable(app).filter(StringUtils::isNotBlank)
                .ifPresent(b -> headers.set("ESZToken", b));
        return new HttpEntity<>(body, headers);
    }

    private static <T> HttpEntity<T> createHeaderEntity(T body, String app, String key, MediaType mediaType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(singletonList(mediaType));
        Optional.ofNullable(app).filter(StringUtils::isNotBlank)
                .ifPresent(b -> headers.set("Esz-Access-Token", b));
        Optional.ofNullable(app).filter(StringUtils::isNotBlank)
                .ifPresent(b -> headers.set("ESZToken", b));
        Optional.ofNullable(key).filter(StringUtils::isNotBlank)
                .ifPresent(b -> headers.add(HEADER_X_API_KEY, b));
        return new HttpEntity<>(body, headers);
    }

    public static ClientHttpResponse getClientHttpResponse(ClientHttpResponse response, byte[] bytes) {
        return new ClientHttpResponse() {
            @Override
            public HttpStatus getStatusCode() throws IOException {
                // fixme kostyl
                return response.getRawStatusCode() == 499 ? HttpStatus.BAD_REQUEST : response.getStatusCode();
            }

            @Override
            public int getRawStatusCode() throws IOException {
                return response.getRawStatusCode();
            }

            @Override
            public String getStatusText() throws IOException {
                return response.getStatusText();
            }

            @Override
            public void close() {
                response.close();
            }

            @Override
            public InputStream getBody() {
                return Optional.ofNullable(bytes).map(ByteArrayInputStream::new)
                        .map(InputStream.class::cast).orElseGet(StreamUtils::emptyInput);
            }

            @Override
            public HttpHeaders getHeaders() {
                return response.getHeaders();
            }
        };
    }
}
