package tech.clink.mesh.porteacher.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Supplier;

@Slf4j
public class Profiler implements AutoCloseable {
    private long startTime;

    public static <T, E extends Throwable> T doSmth(Supplier<T> supplier) {
        try (Profiler ignored = Profiler.create()) {
            return supplier.get();
        } catch (RuntimeException e) {
            throw e;
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public static void doSmth(Runnable runnable) {
        try (Profiler ignored = Profiler.create()) {
            runnable.run();
        } catch (RuntimeException e) {
            throw e;
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    private static Profiler create() {
        Profiler profiler = new Profiler();
        profiler.startTimer();
        return profiler;
    }

    private void startTimer() {
        this.startTime = System.currentTimeMillis();
    }

    @Override
    public void close() {
        long arg = System.currentTimeMillis() - startTime;
        StackTraceElement stackTraceElement = Thread.currentThread().getStackTrace()[3];
        String operation = StringUtils.joinWith(":", stackTraceElement.getClassName(),
                stackTraceElement.getMethodName(), stackTraceElement.getLineNumber());
        log.info("Duration of operation {} = {} ms", operation, arg);
        System.err.println(String.format("Duration of operation %s = %s ms", operation, arg));
    }
}
