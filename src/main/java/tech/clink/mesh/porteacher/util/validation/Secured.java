package tech.clink.mesh.porteacher.util.validation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Secured {
    GlobalRole[] globalRoles() default GlobalRole.ALL;

    boolean urlCookie() default false;

    boolean by<PERSON>erson() default true;

    enum GlobalRole {
        ADMIN, READ_ADMIN, TEACHER, ALL
    }
}