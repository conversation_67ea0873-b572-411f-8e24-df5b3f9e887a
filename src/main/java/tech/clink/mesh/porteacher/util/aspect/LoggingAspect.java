package tech.clink.mesh.porteacher.util.aspect;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableSet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.hibernate.validator.internal.util.ReflectionHelper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.data.util.CastUtils;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionSystemException;
import tech.clink.mesh.porteacher.model.About;
import tech.clink.mesh.porteacher.model.Award;
import tech.clink.mesh.porteacher.model.LogAction;
import tech.clink.mesh.porteacher.model.Teacher;
import tech.clink.mesh.porteacher.model.common.ActionCode;
import tech.clink.mesh.porteacher.model.common.Awardable;
import tech.clink.mesh.porteacher.model.common.DeletableTeacherEntity;
import tech.clink.mesh.porteacher.model.common.IdentityEntity;
import tech.clink.mesh.porteacher.model.dto.AboutAndCredo;
import tech.clink.mesh.porteacher.model.dto.DeletedDTO;
import tech.clink.mesh.porteacher.model.ref.ActionCodeRef;
import tech.clink.mesh.porteacher.rest.AllController;
import tech.clink.mesh.porteacher.rest.api.NegativeResponse;
import tech.clink.mesh.porteacher.service.CrudService;
import tech.clink.mesh.porteacher.service.kafka.ClickHouseService;
import tech.clink.mesh.porteacher.util.Constants;
import tech.clink.mesh.porteacher.util.Utils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.SPACE;
import static org.apache.commons.lang3.StringUtils.substringAfterLast;
import static tech.clink.mesh.porteacher.model.common.ActionCode.*;

@Slf4j
@Aspect
@Order(0)
@Component
@RequiredArgsConstructor
@ConditionalOnProperty("log.enable")
public class LoggingAspect {
    private final ObjectMapper mapper;
    private final CrudService crudService;
    private final HttpServletRequest request;
    private final AllController allController;
    private final ClickHouseService clickHouseService;
    private ObjectMapper loggerMapper;
    private Map<ActionCode, ActionCodeRef> cache;

    @PostConstruct
    private void initialize() {
        loggerMapper = mapper.copy();
        loggerMapper.disable(MapperFeature.USE_ANNOTATIONS);

        cache = crudService.findAllRef(ActionCodeRef.class).stream()
                .collect(Collectors.toMap(ref -> ActionCode.getByValue(ref.getId()),
                        Function.identity(),
                        (v1, v2) -> v1,
                        () -> new EnumMap<>(ActionCode.class)));
    }

    @Around("@annotation(action)")
    public Object logMethods(ProceedingJoinPoint joinPoint, LoggedMethod action) throws Throwable {
        log.info("logMethods");
        Object[] args = Arrays.stream(joinPoint.getArgs())
                .filter(arg -> !(arg instanceof HttpServletRequest))
                .filter(arg -> !(arg instanceof HttpServletResponse))
                .toArray();

        String requestURI = StringUtils.removeStart(StringUtils.removeEnd(
                request.getRequestURI(), Constants.SLASH), Constants.SLASH);
        String method = request.getMethod();
        if (StringUtils.startsWith(requestURI, "teachers")) {
            requestURI = StringUtils.substringAfterLast(requestURI, Constants.SLASH);
        }
        String correctedPath = method + SPACE + requestURI;
        String idInPath = substringAfterLast(requestURI, Constants.SLASH);
        if (StringUtils.isNumeric(idInPath)) {
            correctedPath = StringUtils.removeEnd(correctedPath, Constants.SLASH + idInPath);
        }

        LogAction logAction = new LogAction();
        logAction.setRoute(request.getRequestURI());
        logAction.setRequestBody(Utils.safetyGet(() -> loggerMapper.valueToTree(args)));
        logAction.setStaffId(Utils.safetyGet(() -> crudService.getTokenPayloadThr().getStf()));
        setActionCodePreProceed(logAction, args, action, method, correctedPath);

        Object proceed;
        try {
            proceed = joinPoint.proceed();
            JsonNode result;
            if ("GET logs".equals(correctedPath)) {
                result = ObjectUtils.defaultIfNull(
                        Utils.safetyGet(() -> mapper.valueToTree(proceed)),
                        mapper.nullNode());
            } else {
                result = mapper.valueToTree(proceed);
            }

            setActionCodePostProceed(logAction, correctedPath, result);
            logAction.setResponseBody(result);
            logAction.setIsSuccessful(Boolean.TRUE);
            saveLog(logAction);

            return proceed;
        } catch (TransactionSystemException e) {
            NegativeResponse<String> response = allController.handleException(e);
            logException(logAction, response);
            throw e;
        } catch (Exception e) {
            NegativeResponse<String> response = allController.handleException(e);
            logException(logAction, response);
            throw e;
        }
    }

    private void setActionCodePreProceed(LogAction logAction, Object[] args, LoggedMethod action,
                                         String method, String correctedPath) {
        if (Objects.isNull(logAction.getStaffId())) return;

        ActionCode actionCode = action.value();

        if (NONE.equals(actionCode)) {
            if (method.equals(HttpMethod.GET.name())
                    && !PATH2NAME.containsKey(correctedPath)) { // 10
                actionCode = GETTING_INFORMATION;
            } else {
                actionCode = PATH2NAME.get(correctedPath);
            }
        }

        if (PUT2DELETE.contains(correctedPath)) {
            Object input = getFromArray(args,
                    element -> Iterable.class.isAssignableFrom(element.getClass())
                            || IdentityEntity.class.isAssignableFrom(element.getClass()));

            if (Objects.nonNull(input)) {
                if (About.class.isAssignableFrom(input.getClass())) {
                    if (isAboutEmpty(CastUtils.cast(input))) {
                        actionCode = DELETING_DATA_OF_PORTFOLIO;
                    }
                } else if (ReflectionHelper.isIterable(input.getClass())) {
                    List<DeletableTeacherEntity> list = CastUtils.cast(input);
                    if (list.stream().allMatch(deletable -> Boolean.TRUE.equals(deletable.getIsDeleted()))) {
                        actionCode = DELETING_DATA_OF_PORTFOLIO;
                    }
                }
            }
        }
        if (LINKING.contains(correctedPath)) {
            Awardable input = CastUtils.cast(getFromArray(args,
                    element -> Awardable.class.isAssignableFrom(element.getClass())));
            Map<Boolean, Set<Long>> inputAwards = CollectionUtils.emptyIfNull(input.getAws()).stream()
                    .collect(Collectors.groupingBy(DeletedDTO::getIsDeleted,
                            Collectors.mapping(DeletedDTO::getId, Collectors.toSet())));

            if (Objects.isNull(input.getId())) {
                if (CollectionUtils.isNotEmpty(inputAwards.get(Boolean.FALSE))) {
                    actionCode = LINKING_DATA_TO_PORTFOLIO;
                }
            } else {
                Awardable before = CastUtils.cast(crudService.find(input.getClass(), input.getId()));
                Set<Long> beforeAwards = Utils.extract(before.getAwards(), Award::getId);
                int toAddCount = SetUtils.difference(SetUtils.emptyIfNull(inputAwards.get(Boolean.FALSE)),
                        beforeAwards).size();
                int toDeleteCount = SetUtils.intersection(SetUtils.emptyIfNull(inputAwards.get(Boolean.TRUE)),
                        beforeAwards).size();
                if ((toAddCount > 0) || (toDeleteCount > 0)) {
                    actionCode = LINKING_DATA_TO_PORTFOLIO;
                }
            }
        }

        logAction.setActionCodeRef(cache.get(actionCode));
    }

    private Object getFromArray(Object[] array, Predicate<? super Object> filter) {
        return Arrays.stream(array)
                .filter(filter)
                .findFirst()
                .orElse(null);
    }

    private Boolean isAboutEmpty(About about) {
        AboutAndCredo info = about.getInfo();

        return Objects.nonNull(info)
                && StringUtils.EMPTY.equals(info.getInfo())
                && StringUtils.EMPTY.equals(info.getQuote())
                && StringUtils.EMPTY.equals(info.getQuoteAuthor());
    }

    private void setActionCodePostProceed(LogAction logAction, String correctedPath, JsonNode result) {
        if (Objects.isNull(logAction.getStaffId())) {
            logAction.setStaffId(Utils.safetyGet(
                    () -> crudService.find(Teacher.class,
                            crudService.getShareLinkThr().getTeacherId()).getStaffId()));
            logAction.setActionCodeRef(cache.get(UNAUTHORIZED_USER_ACTION));

            return;
        }

        if (BY_SYSTEM.contains(correctedPath)) {
            if (BooleanUtils.isTrue(Utils.safetyGet(
                    () -> Constants.SYSTEM.equals(result.get("data").get("createdBy").asText())))) {
                logAction.setActionCodeRef(cache.get(ADDING_FILE_TO_DATA_LOADED_AUTOMATICALLY));
            }
        }
    }

    private void saveLog(LogAction logAction) {
        logAction = crudService.logAction(logAction);
        logAction.reachTransient(crudService);
        clickHouseService.sendMessage(logAction);
    }

    private void logException(LogAction logAction, NegativeResponse<String> response) {
        logAction.setResponseBody(mapper.valueToTree(response));
        logAction.setIsSuccessful(Boolean.FALSE);
        saveLog(logAction);
    }

    //3
    public static final Set<String> BY_SYSTEM = ImmutableSet.of(
            "PATCH events", "PATCH additional-educations");

    //4
    public static final Set<String> LINKING = ImmutableSet.of(
            "PATCH events", "PATCH projects", "POST events", "POST projects");

    //5
    public static final Set<String> PUT2DELETE = ImmutableSet.of(
            "POST about-me", "POST credo", "PUT reference-cards", "PUT professional-interest", "PUT subjects");

    public static final Map<String, ActionCode> PATH2NAME = new HashMap<>() {{
        // 1
        put("POST awards", UPLOADING_DATA_TO_PORTFOLIO);
        put("POST events", UPLOADING_DATA_TO_PORTFOLIO);
        put("POST projects", UPLOADING_DATA_TO_PORTFOLIO);
        put("POST additional-educations", UPLOADING_DATA_TO_PORTFOLIO);

        // 2
        put("PATCH awards", EDITING_DATA_OF_PORTFOLIO);
        put("PATCH events", EDITING_DATA_OF_PORTFOLIO);
        put("PATCH projects", EDITING_DATA_OF_PORTFOLIO);
        put("PATCH additional-educations", EDITING_DATA_OF_PORTFOLIO);

        // 7
        put("POST logs/qr", DOWNLOADING_QR_CODE);
        // 8
        put("POST logs/pdf", DOWNLOADING_PDF);
        // 9
        put("POST logs/attachment", DOWNLOADING_FILE);
        //11
        put("POST documents", UPLOADING_FILE);
        //12
        put("GET diagnostics", OTHER);
        put("PATCH educations", OTHER);
        put("PATCH academic-titles", OTHER);
        put("PATCH academic-degrees", OTHER);
        put("GET gratitude-students", OTHER);
        put("PATCH gratitude-students", OTHER);
        put("DELETE gratitude-students", OTHER);
    }};
}
