server.servlet.contextPath=/${SERVER_PATH:}
server.port=${SERVER_PORT:8080}
server.entity=${SERVER_ENTITY:DocumentStorage}

spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=200MB

roles.teacherId=${HEAD_TEACHER_GLOBAL_ROLE_ID:202:17}
roles.adminId=${HEAD_ADMIN_GLOBAL_ROLE_ID:203:17}
roles.readAdminId=${HEAD_READ_ADMIN_GLOBAL_ROLE_ID:204:17}
auds.teacher=${HEAD_TEACHER_GLOBAL_AUD:17:9}
auds.admin=${HEAD_ADMIN_GLOBAL_AUD:17:16}

auth.enable=${AUTH_ENABLE:true}
log.enable=${LOG_ENABLE:true}

external.service.ceds.url=${CEDS_HOST:}
external.service.ceds.link=${CEDS_LINK:}
external.service.ceds.systemCode=${CEDS_SYSTEM_CODE:ais_pf_uchit_dit}
external.service.ceds.password=${CEDS_PASSWORD:}
external.service.ceds.repository=${CEDS_REPOSITORY:DIT}
external.service.ceds.clazz=${CEDS_DOC_CLASS:StudentAchievement}

logging.level.root = info

springfox.documentation.open-api.v3.paths=/v4/api-doc
springfox.documentation.open-api.v3.path=/v4/api-doc
springfox.documentation.swagger-ui.base-url=/my
springdoc.api-docs.path=/contract

# Database configuration
spring.datasource.originUrl=${DATASOURCE_HOST:************}
spring.datasource.originPort=${DATASOURCE_PORT:6432}
spring.datasource.url=jdbc:postgresql://${spring.datasource.originUrl}:${spring.datasource.originPort}/${DATASOURCE_DB:teacher_dev_rw}
spring.datasource.username=${DATASOURCE_USERNAME:}
spring.datasource.password=${DATASOURCE_PASSWORD:}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.schema=${DATASOURCE_SCHEMA:public}
spring.jpa.properties.hibernate.default_schema=${DATASOURCE_SCHEMA:public}
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyComponentPathImpl
spring.jpa.hibernate.naming.physical-strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
spring.jackson.deserialization.use_long_for_ints=true
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false

spring.datasource.replica.enable=${DATASOURCE_REPLICA_ENABLE:false}
spring.datasource.replica.hosts=${DATASOURCE_REPLICA_HOSTS:${spring.datasource.originUrl},${spring.datasource.originUrl}}
spring.datasource.replica.ports=${DATASOURCE_REPLICA_PORTS:6432,6432}
spring.datasource.replica.users=${DATASOURCE_REPLICA_USERS:${spring.datasource.username},${spring.datasource.username}}
spring.datasource.replica.passwords=${DATASOURCE_REPLICA_PASSWORDS:${spring.datasource.password},${spring.datasource.password}}

management.endpoint.prometheus.enabled=true
management.metrics.export.prometheus.enabled=true
management.endpoints.web.exposure.include=health,prometheus

click.house.kafka.enable=${CLICK_HOUSE_KAFKA_ENABLE:false}
click.house.kafka.bootstrap-servers=${CLICK_HOUSE_KAFKA_SERVERS:***********:9092}
click.house.kafka.topics=${CLICK_HOUSE_KAFKA_TOPICS:logs.fct.teacherportfolio-events.0}
click.house.kafka.security-enable=${CLICK_HOUSE_KAFKA_SECURITY:false}
click.house.kafka.properties-sasl-mechanism=PLAIN
click.house.kafka.properties-security-protocol=SASL_PLAINTEXT
click.house.kafka.properties-sasl-jaas-config=

server.ext.s3.enable=${S3_ENABLE:true}
access.key.id=${S3_ACCESS_KEY_ID:AKIAIOSFODNN7EXAMPLE}
access.key.secret=${S3_ACCESS_KEY_SECRET:}
s3.region.name=${S3_REGION_NAME:us-west}
s3.bucket.name=${S3_BUCKET_NAME:consent}
s3.address=${S3_HOST:}
s3.proxy=${S3_PROXY:}

scheduler.enable=${SCHEDULER_ENABLE:true}
scheduler.deleteMaterialsByOne=${SCHEDULER_DEL_MATERIALS_FOR:true}
scheduler.periodDeleteMaterials=${SCHEDULER_DEL_MATERIALS:0 0 0 * * SAT}
scheduler.partitionDeleteMaterials=${SCHEDULER_PART_MATERIALS:1000}

aupd.core.keyName=${AUPD_KEY_NAME:key-dev.cer}
