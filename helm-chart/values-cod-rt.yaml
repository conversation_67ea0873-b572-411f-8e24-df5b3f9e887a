env: prod-tatarstan
tier: backend

deployment:
  deploymentAnnotations:
  replicaCount: 1
  image:
    repository: harbor-edu.mos.ru/mes-teacherportfolio/s3integration
    tag: "latest"
    pullPolicy: IfNotPresent
  port: 8080
  resources:
    #limits:
    #  cpu: 2
    #  memory: 2048Mi
    requests:
      cpu: 1
      memory: 2Gi
  restartPolicy: Always
  volume: |-
    - name: {{ .Chart.Name }}
      configMap:
        name: {{ .Chart.Name }}
        defaultMode: 0777
        items:
          - key: run.sh
            path: run.sh
  volumeMounts: |-
    - name: {{ .Chart.Name }}
      mountPath: /opt/run.sh
      subPath: run.sh
  livenessProbe:
   path: /actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
  readinessProbe:
   path: /actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
   readnessPeriod: 5

service:
  type: ClusterIP
  port: 80
  targetPort: 8080

ingress:
  enabled: true
  name: s3integration
  url: ms-kubernetes-api.16-tat
  pathType: Prefix
  path: /teacherportfolio/documents/v1
  servicePort: 80

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

configmap:
  application:
    JAR_APP_PATH: /opt/s3Integration.jar
    SERVER_PATH:
    SERVER_PORT: 8080
    SERVER_ENTITY: DocumentStorage
    DATASOURCE_HOST: ms16-db-prtch01p
    DATASOURCE_PORT: 5432
    DATASOURCE_DB: teacherportfolio
    DATASOURCE_USERNAME: portfolio
    DATASOURCE_PASSWORD: myHqsRkS9n9@
    DATASOURCE_SCHEMA: public
    DATASOURCE_REPLICA_HOSTS: ms16-db-prtch02p
    DATASOURCE_REPLICA_PORTS: 5432
    DATASOURCE_REPLICA_USERS: portfolio
    DATASOURCE_REPLICA_PASSWORDS: myHqsRkS9n9@
    DATASOURCE_REPLICA_ENABLE: false
    S3_ENABLE: true
    S3_BUCKET_NAME: tat16-prtch
    S3_HOST: https://s3.tatar.ru
    S3_ACCESS_KEY_ID: tat16-prtf
    S3_ACCESS_KEY_SECRET: oob8etohB5yi5veeV4
    S3_PROXY: https://ms-edu.tatar.ru/storage
    CEDS_HOST:
    CEDS_LINK:
    CEDS_SYSTEM_CODE:
    CEDS_PASSWORD:
    CEDS_REPOSITORY:
    CEDS_DOC_CLASS:
