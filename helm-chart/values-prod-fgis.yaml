env: prod-fgis
tier: backend

deployment:
  deploymentAnnotations:
  replicaCount: 1
  image:
    repository: harbor-edu.mos.ru/mes-teacherportfolio/s3integration
    tag: "latest"
    pullPolicy: IfNotPresent
  port: 8080
  resources:
    #limits:
    #  cpu: 2
    #  memory: 2048Mi
    requests:
      cpu: 1
      memory: 2Gi
  restartPolicy: Always
  volume: |-
    - name: {{ .Chart.Name }}
      configMap:
        name: {{ .Chart.Name }}
        defaultMode: 0777
        items:
          - key: run.sh
            path: run.sh
  volumeMounts: |-
    - name: {{ .Chart.Name }}
      mountPath: /opt/run.sh
      subPath: run.sh
  livenessProbe:
   path: /actuator/health/
   port: 8080
   initDelay: 120
   failThreshold: 3
  readinessProbe:
   path: /actuator/health/
   port: 8080
   initDelay: 120
   failThreshold: 3
   readnessPeriod: 5

service:
  type: ClusterIP
  port: 8080
  targetPort: 8080

ingress:
  enabled: true
  name: s3integration
  url: kub-lb01p.astra.mesh.sitronics.com
  pathType: Prefix
  path: /teacherportfolio/teacher/v1/s3
  servicePort: 8080

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

configmap:
  application:
    JAR_APP_PATH: /opt/s3Integration.jar
    SERVER_PATH:
    SERVER_PORT: 8080
    SERVER_ENTITY: false
    DATASOURCE_HOST: prtch-db01p
    DATASOURCE_PORT: 5432
    DATASOURCE_DB: teacherportfolio
    DATASOURCE_USERNAME: portfolio
    DATASOURCE_PASSWORD: myHqsRkS9n9@
    DATASOURCE_SCHEMA: public
    DATASOURCE_REPLICA_HOSTS: prtch-db02p
    DATASOURCE_REPLICA_PORTS: 5432
    DATASOURCE_REPLICA_USERS: portfolio
    DATASOURCE_REPLICA_PASSWORDS: myHqsRkS9n9@
    DATASOURCE_REPLICA_ENABLE: false
    S3_ENABLE: true
    S3_BUCKET_NAME: p-teacher
    S3_HOST: **********:9000
    S3_ACCESS_KEY_ID: IwzOKaKxO30Urb6Q
    S3_ACCESS_KEY_SECRET: ebNkdpwXIvGA1k3Q1TIDVRSnLW7xOPCs
    AUPD_KEY_NAME: key.cer