apiVersion: v1
kind: Service
metadata:
  name: {{ include "name" . }}
  labels: {{- include "labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
      appProtocol: {{ default "http" .Values.service.appProtocol }}
  selector:
  {{- include "selectorLabels" . | nindent 4 }}
