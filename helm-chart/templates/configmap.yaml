apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "name" . }}
  namespace: {{ .Values.namespace }}
data:
  {{- with .Values.configmap.application }}
  run.sh: |
    #!/bin/bash
    clear
    # application jar path
    JAR_APP_PATH={{ .JAR_APP_PATH }} \
    # env conf variables
      SERVER_PATH={{ .SERVER_PATH }} \
      SERVER_PORT={{ .SERVER_PORT }} \
      SERVER_ENTITY={{ .SERVER_ENTITY }} \
      DATASOURCE_HOST={{ .DATASOURCE_HOST }} \
      DATASOURCE_PORT={{ .DATASOURCE_PORT }} \
      DATASOURCE_DB={{ .DATASOURCE_DB }} \
      DATASOURCE_USERNAME={{ .DATASOURCE_USERNAME }} \
      DATASOURCE_PASSWORD={{ .DATASOURCE_PASSWORD }} \
      DATASOURCE_SCHEMA={{ .DATASOURCE_SCHEMA }} \
      DATASOURCE_REPLICA_HOSTS={{ .DATASOURCE_REPLICA_HOSTS}} \
      DATASOURCE_REPLICA_PORTS={{ .DATASOURCE_REPLICA_PORTS }} \
      DATASOURCE_REPLICA_USERS={{ .DATASOURCE_REPLICA_USERS }} \
      DATASOURCE_REPLICA_PASSWORDS={{ .DATASOURCE_REPLICA_PASSWORDS }} \
      DATASOURCE_REPLICA_ENABLE={{ .DATASOURCE_REPLICA_ENABLE }} \
      S3_ENABLE={{ .S3_ENABLE }} \
      S3_BUCKET_NAME={{ .S3_BUCKET_NAME }} \
      S3_HOST={{ .S3_HOST }} \
      S3_ACCESS_KEY_ID={{ .S3_ACCESS_KEY_ID }} \
      S3_ACCESS_KEY_SECRET={{ .S3_ACCESS_KEY_SECRET }} \
      S3_PROXY={{ .S3_PROXY }} \
      CEDS_HOST={{ .CEDS_HOST }} \
      CEDS_LINK={{ .CEDS_LINK }} \
      CEDS_SYSTEM_CODE={{ .CEDS_SYSTEM_CODE }} \
      CEDS_PASSWORD={{ .CEDS_PASSWORD }} \
      CEDS_REPOSITORY={{ .CEDS_REPOSITORY }} \
      CEDS_DOC_CLASS={{ .CEDS_DOC_CLASS }} \
      AUPD_KEY_NAME={{ .AUPD_KEY_NAME }} \
      JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 \
      java -jar ${JAR_APP_PATH}
  {{ end }}
