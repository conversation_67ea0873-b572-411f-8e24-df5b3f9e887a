{{- define "name" -}}
{{- default .Chart.Name | trunc 63 | trimSuffix "-" }}
{{- end }}

{{- define "labels" -}}
{{ include "selectorLabels" . }}
app.kubernetes.io/environment: {{ .Values.env }}
app.kubernetes.io/component: {{ .Values.tier }}
{{- end }}

{{- define "selectorLabels" -}}
app.kubernetes.io/app: {{ include "name" . }}
{{- end }}

{{- define "volumeMounts" -}}
{{- end }}

{{- define "volumes" -}}
{{- end }}

{{- define "ingressAnnotations" -}}
kubernetes.io/ingress.class: {{ .Values.ingress.class }}
{{- end }}
