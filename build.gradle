plugins {
    id 'org.springframework.boot' version '2.5.0'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id "com.peterabeles.gversion" version "1.10"
    id 'checkstyle'
    id 'java'
}

group = 'tech.clink.mesh.porteacher'
version = '1.0.0'
sourceCompatibility = '11'
targetCompatibility = '11'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
    gradlePluginPortal()
    flatDir {
        dirs 'libs'
    }
}

bootJar {
    archiveFileName = "s3Integration.jar"
}

gversion {
    srcDir = "src/main/java/"
    classPackage = "tech.clink.mesh.porteacher.util"
    className = "Version"
    dateFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'"
    timeZone = "UTC"
}

project.compileJava.dependsOn(createVersionFile)

dependencies {
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
//    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-hateoas'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.kafka:spring-kafka'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    runtimeOnly 'org.postgresql:postgresql'

    implementation 'com.vladmihalcea:hibernate-types-52:2.3.1'
    implementation "io.springfox:springfox-boot-starter:3.0.0"
    implementation "org.springdoc:springdoc-openapi-ui:1.6.6"

    implementation 'javax.validation:validation-api:2.0.1.Final'
    implementation 'io.micrometer:micrometer-registry-prometheus'

    implementation 'org.apache.logging.log4j:log4j-api:2.17.1'
    implementation 'org.apache.logging.log4j:log4j-to-slf4j:2.17.1'
    implementation 'net.logstash.logback:logstash-logback-encoder:6.1'
    implementation 'ch.qos.logback.contrib:logback-json-classic:0.1.5'
    implementation 'ch.qos.logback.contrib:logback-jackson:0.1.5'
    implementation 'ch.qos.logback:logback-classic:1.2.6'

    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.7.3'

    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'commons-beanutils:commons-beanutils:1.9.4'
    implementation 'org.apache.commons:commons-lang3:3.11'
    implementation 'org.apache.commons:commons-io:1.3.2'
    implementation 'com.google.guava:guava:28.0-jre'

    implementation group: 'com.nimbusds', name: 'nimbus-jose-jwt', version: '6.2'
    implementation group: 'javax.mail', name: 'mail', version: '1.4.7'

    implementation group: 'org.reflections', name: 'reflections', version: '0.9.11'

    implementation platform('com.amazonaws:aws-java-sdk-bom:1.11.837')
    implementation 'com.amazonaws:aws-java-sdk-s3'
}

checkstyleMain.include '**/porteacher/model/**'

task copySh {
    doLast {
        copy {
            from 'run.sh'
            into "build/libs/"
        }
    }
}

task release {
    dependsOn 'build'
    dependsOn 'copySh'
}
