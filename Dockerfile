FROM harbor-edu.mos.ru/docker_proxy_cod/gradle:7.4.2-jdk17 AS build
COPY --chown=gradle:gradle . /home/<USER>/src
WORKDIR /home/<USER>/src
RUN gradle clean build -Dorg.gradle.jvmargs=-Xmx2048M -x test
RUN ls -lha /home/<USER>/src/build/libs

FROM harbor-edu.mos.ru/docker_proxy_cod/bellsoft/liberica-openjdk-debian:17
# todo jar name from parameter ${appName}.jar
COPY --from=build /home/<USER>/src/build/libs/s3Integration.jar /opt/s3Integration.jar
WORKDIR /opt
CMD ["./run.sh"]