#!/bin/bash
# Usage:
# ./notify.sh
TIME="10"

URL="https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage"

text="Коллеги, на ИС +*$CI_PROJECT_NAMESPACE*, выполнена установка приложения +*$CI_PROJECT_NAME*
Коммит:+*${CI_COMMIT_MESSAGE%$'\n'}:${CI_COMMIT_SHORT_SHA}*.
Ветка:+*${CI_COMMIT_REF_NAME}*.
Задачу запустил:+*${GITLAB_USER_NAME}*.
Окружение:+*$CLUSTER*.
Время установки: +*$(date -d "@$(( $(date +%s) + 10800 ))" "+%H:%M:%S %d_%m_%Y")*
#deploy"

if [ "$CLUSTER" == "DEV, DEV-K8S, STAGE" ]
        then
curl -s -X POST --connect-timeout 10 $URL -d chat_id=$TELEGRAM_USER_ID_AUPD_DEV -d parse_mode="Markdown" -d text="$text" #> /dev/null
        else
curl -s -X POST --connect-timeout 10 $URL -d chat_id=$TELEGRAM_USER_ID_PORTUCH -d parse_mode="Markdown" -d text="$text" #> /dev/null
fi

if [[ "$CLUSTER" == "Prod-Kaluga" ]]  
    then 
        curl -v -s -X POST --connect-timeout 10 $URL -d chat_id=$TELEGRAM_CHAT_ID_KALUGA -d parse_mode="Markdown" -d text="$text" #> /dev/null
fi

if [[ "$CLUSTER" == "Prod-MO" ]]  
    then 
        curl -v -s -X POST --connect-timeout 10 $URL -d chat_id=$TELEGRAM_CHAT_ID_MO -d parse_mode="Markdown" -d text="$text" #> /dev/null
fi

if [[ "$CLUSTER" == "Prod-Dagestan" ]]  
    then 
        curl -v -s -X POST --connect-timeout 10 $URL -d chat_id=$TELEGRAM_CHAT_ID_DAGESTAN -d parse_mode="Markdown" -d text="$text" #> /dev/null
fi
